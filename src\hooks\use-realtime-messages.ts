import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSocket } from '@/contexts/SocketContext';
import { QUERY_KEYS } from '@/hooks/use-game-api';

interface Message {
  message_id: string;
  content: string;
  sender: {
    username: string;
    is_admin: boolean;
    is_moderator: boolean;
  };
  created_at: string;
  reactions: Array<{
    emoji: string;
    count: number;
  }>;
  message_type: string;
  reply_to?: string;
}

interface RealtimeMessageHookProps {
  channelId: string;
  enabled?: boolean;
  onNewMessage?: (message: Message) => void;
  onReactionUpdate?: (data: any) => void;
  onMessageDeleted?: (messageId: string) => void;
}

export const useRealtimeMessages = ({
  channelId,
  enabled = true,
  onNewMessage,
  onReactionUpdate,
  onMessageDeleted,
}: RealtimeMessageHookProps) => {
  const { socket, isConnected, joinChannel, leaveChannel } = useSocket();
  const queryClient = useQueryClient();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const previousChannelRef = useRef<string | null>(null);

  // Auto-scroll to bottom function
  const scrollToBottom = useCallback((smooth = true) => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      const scrollOptions: ScrollToOptions = {
        top: container.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      };
      container.scrollTo(scrollOptions);
    }
  }, []);

  // Check if user is at bottom of messages
  const isAtBottom = useCallback(() => {
    if (!messagesContainerRef.current) return true;
    
    const container = messagesContainerRef.current;
    const threshold = 100; // pixels from bottom
    return (
      container.scrollHeight - container.scrollTop - container.clientHeight < threshold
    );
  }, []);

  // Join/leave channels when channelId changes
  useEffect(() => {
    if (!enabled || !isConnected || !channelId) return;

    // Leave previous channel if it exists
    if (previousChannelRef.current && previousChannelRef.current !== channelId) {
      leaveChannel(previousChannelRef.current);
    }

    // Join new channel
    joinChannel(channelId);
    previousChannelRef.current = channelId;

    // Cleanup: leave channel when component unmounts or channelId changes
    return () => {
      if (channelId) {
        leaveChannel(channelId);
      }
    };
  }, [channelId, isConnected, enabled, joinChannel, leaveChannel]);

  // Set up socket event listeners
  useEffect(() => {
    if (!socket || !enabled || !channelId) return;

    const handleNewMessage = (data: any) => {
      console.log('New message received:', data);
      
      // Check if user was at bottom before new message
      const wasAtBottom = isAtBottom();
      
      // Update the messages cache
      queryClient.setQueryData(
        QUERY_KEYS.CHANNEL_MESSAGES(channelId),
        (oldData: any) => {
          if (!oldData) return oldData;
          
          const newMessage = {
            message_id: data.message.message_id,
            content: data.message.content,
            sender: data.message.sender,
            created_at: data.message.created_at,
            reactions: data.message.reactions || [],
            message_type: data.message.message_type || 'text',
            reply_to: data.message.reply_to,
          };
          
          return {
            ...oldData,
            messages: [...(oldData.messages || []), newMessage],
          };
        }
      );

      // Call custom callback if provided
      if (onNewMessage) {
        onNewMessage(data);
      }

      // Auto-scroll to bottom if user was already at bottom
      if (wasAtBottom) {
        // Small delay to ensure DOM is updated
        setTimeout(() => scrollToBottom(true), 100);
      }
    };

    const handleReactionUpdate = (data: any) => {
      console.log('Reaction update received:', data);
      
      // Update the specific message's reactions in cache
      queryClient.setQueryData(
        QUERY_KEYS.CHANNEL_MESSAGES(channelId),
        (oldData: any) => {
          if (!oldData) return oldData;
          
          return {
            ...oldData,
            messages: oldData.messages.map((message: Message) =>
              message.message_id === data.message_id
                ? { ...message, reactions: data.reactions }
                : message
            ),
          };
        }
      );

      // Call custom callback if provided
      if (onReactionUpdate) {
        onReactionUpdate(data);
      }
    };

    const handleMessageDeleted = (data: any) => {
      console.log('Message deleted:', data);
      
      // Remove the message from cache
      queryClient.setQueryData(
        QUERY_KEYS.CHANNEL_MESSAGES(channelId),
        (oldData: any) => {
          if (!oldData) return oldData;
          
          return {
            ...oldData,
            messages: oldData.messages.filter(
              (message: Message) => message.message_id !== data.message_id
            ),
          };
        }
      );

      // Call custom callback if provided
      if (onMessageDeleted) {
        onMessageDeleted(data.message_id);
      }
    };

    // Set up event listeners
    const newMessageEvent = `channel_${channelId}_newMessage`;
    const reactionUpdateEvent = `channel_${channelId}_reactionUpdate`;
    const messageDeletedEvent = `channel_${channelId}_messageDeleted`;

    socket.on(newMessageEvent, handleNewMessage);
    socket.on(reactionUpdateEvent, handleReactionUpdate);
    socket.on(messageDeletedEvent, handleMessageDeleted);

    // Cleanup event listeners
    return () => {
      socket.off(newMessageEvent, handleNewMessage);
      socket.off(reactionUpdateEvent, handleReactionUpdate);
      socket.off(messageDeletedEvent, handleMessageDeleted);
    };
  }, [
    socket,
    channelId,
    enabled,
    queryClient,
    onNewMessage,
    onReactionUpdate,
    onMessageDeleted,
    isAtBottom,
    scrollToBottom,
  ]);

  return {
    messagesContainerRef,
    scrollToBottom,
    isAtBottom,
    isConnected,
  };
};
