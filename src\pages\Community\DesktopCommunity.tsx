import { useState, useEffect } from 'react';
import { Send, Hash, Users, MessageCircle, Smile, Pin, Search, Filter, Crown, Zap, Star, Trophy, Shield, Globe, Heart, Sparkles, Trash2, Wifi, WifiOff } from 'lucide-react';
import { useWallet } from '@/contexts/WalletContext';
import { useChannels, useSendMessage, useAddReaction, useChannelMessages } from "@/hooks/use-game-api";
import { Channel } from '@/types/api';
import { useGame } from '@/contexts/GameContext';
import { Button } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { useRealtimeMessages } from '@/hooks/use-realtime-messages';
import { useSocket } from '@/contexts/SocketContext';

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes} mins ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} days ago`;

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) return `${diffInWeeks} weeks ago`;

  return date.toLocaleDateString();
};

const DesktopCommunity = () => {
  const [activeChannel, setActiveChannel] = useState('_general');
  const [message, setMessage] = useState('');
  // const { userData, address, connectWallet, disconnectWallet, authToken } = useWallet(); // Assuming authToken is available
  const {
    authToken,
    userData,
    selectedGame,
    gameStats,
    showAccountConnectModal,
    setShowAccountConnectModal,
    hasAuthToken,
  } = useGame();
  const { data: apiChannels, isLoading: channelsLoading, error: channelsError } = useChannels();
  const {
    data: channelData,
    isLoading: messagesLoading,
    error: messagesError,
    refetch: refetchMessages
  } = useChannelMessages(activeChannel, authToken || "", {
    enabled: Boolean(authToken && activeChannel && userData), // Wait for all required data
  })

  // Socket connection status
  const { isConnected: socketConnected } = useSocket();

  // Real-time messaging
  const { messagesContainerRef, scrollToBottom, isAtBottom } = useRealtimeMessages({
    channelId: activeChannel,
    enabled: Boolean(authToken && userData),
    onNewMessage: (newMessage) => {
      console.log('New message received in UI:', newMessage);
      // Additional UI updates can be handled here if needed
    },
    onReactionUpdate: (data) => {
      console.log('Reaction updated in UI:', data);
      // Additional UI updates can be handled here if needed
    },
    onMessageDeleted: (messageId) => {
      console.log('Message deleted in UI:', messageId);
      // Additional UI updates can be handled here if needed
    },
  })

  // Scroll to bottom when channel changes or messages load
  useEffect(() => {
    if (channelData?.messages && channelData.messages.length > 0) {
      // Small delay to ensure DOM is updated
      setTimeout(() => scrollToBottom(false), 100);
    }
  }, [activeChannel, channelData?.messages, scrollToBottom]);;

  // Chat mutations
  const sendMessageMutation = useSendMessage();
  const addReactionMutation = useAddReaction();
  // const deleteMessageMutation = useDeleteMessage();

  // TODO: Add this hook to fetch messages for active channel
  // const { data: channelMessages } = useChannelMessages(activeChannelId);

  const categories = {
    'text': 'Text Channels',
    'voice': 'Voice Channels',
    'announcement': 'Announcement Channels',
  }

  const channels = apiChannels?.map((channel: Channel, index: number) => ({
    id: channel.channel_id, // Use channel_id instead of channel_name
    name: `# ${channel.channel_name}`,
    members: channel.member_count,
    online: channel.online_count,
    only_admin: channel.only_admin,
    icon: channel.channel_id === '_general' ? '💬' :
      channel.channel_id === '_announcements' ? '📢' :
        channel.channel_type === 'gaming' ? '🎮' :
          channel.channel_type === 'trading' ? '📈' :
            channel.channel_type === 'support' ? '🛟' : '💬',
    only_moderators: channel.only_moderators,
    pinned_messages: channel.pinned_messages,
    category: categories[channel.channel_type],
  })) || [];

  // TODO: Replace with actual messages from API
  const messages = channelData?.messages.map((msg) => ({
    id: msg.message_id,
    message_id: msg.message_id,
    user: msg.sender.username,
    // avatar: msg.sender.avatar || getDefaultAvatar(msg.sender.username),
    message: msg.content,
    timestamp: formatTimestamp((msg.created_at || new Date()).toString()),
    reactions: msg.reactions,
    pinned: false, // TODO: Implement pinned messages logic
    // level: msg.sender.level,
    // badges: getUserBadges(msg.sender), // Helper function to get badges
    sender: msg.sender,
    staff: msg.sender.is_admin || msg.sender.is_moderator,
    // status: msg.status,
    message_type: msg.message_type,
    reply_to: msg.reply_to,
  })) || [];

  const communityStats = {
    totalMembers: 156789,
    onlineNow: 8,
    messagesLastHour: 1247,
    activeChannels: channels.length,
  };

  const handleSendMessage = async () => {
    if (message.trim() && userData && authToken) {
      try {
        await sendMessageMutation.mutateAsync({
          channelId: activeChannel,
          content: message.trim(),
          authToken: authToken,
          messageType: "text",
        });
        setMessage('');

        // Scroll to bottom after sending message
        setTimeout(() => scrollToBottom(true), 100);

        // Note: No need to refetch messages manually as real-time updates will handle it
      } catch (error) {
        console.error('Failed to send message:', error);
      }
    }
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    if (!userData || !authToken) return;

    try {
      await addReactionMutation.mutateAsync({
        channelId: activeChannel,
        messageId: messageId,
        emoji: emoji,
        authToken: authToken,
      });

      // Note: No need to refetch messages manually as real-time updates will handle it
    } catch (error) {
      console.error('Failed to add reaction:', error);
    }
  };

  // const handleDeleteMessage = async (messageId: string) => {
  //   if (!userData || !authToken) return;

  //   try {
  //     await deleteMessageMutation.mutateAsync({
  //       channelId: activeChannel,
  //       messageId: messageId,
  //       authToken: authToken,
  //     });
  //   } catch (error) {
  //     console.error('Failed to delete message:', error);
  //   }
  // };

  const getChannelsByCategory = (category: string) => {
    return channels.filter(channel => channel.category === category);
  };

  // Check if user can send message in current channel
  const canSendMessage = () => {
    const currentChannel = channels.find(c => c.id === activeChannel);
    if (!currentChannel) return false;

    // TODO: Add user role checking logic here
    // For now, assume user can send if connected
    return userData;
  };

  // Check if user can delete a specific message
  const canDeleteMessage = (messageData: any) => {
    if (!userData) return false;
    // TODO: Add proper user role checking
    // For now, allow deletion if user is sender or admin/moderator
    return true; // Placeholder
  };

  // Loading states
  if (channelsLoading) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center">
        <div className="text-white flex items-center gap-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-400"></div>
          Loading channels...
        </div>
      </div>
    );
  }

  if (channelsError) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center">
        <div className="text-red-400">Error loading channels: {channelsError.message}</div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="fixed inset-0 digital-grid opacity-10 pointer-events-none"></div>

        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-12 text-center max-w-md mx-auto hover:border-accent-cyan/30 transition-all duration-300">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="h-12 w-12 text-accent-cyan" />
              </div>
              <div className="text-6xl mb-4">🎮</div>
            </div>
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Account
            </h2>
            <p className="text-secondary-text mb-6 leading-relaxed">
              You need to connect your account to play games and earn epic
              rewards in the gaming multiverse.
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold px-8 py-3 hover:scale-105 transition-all duration-300"
            >
              <Link to="/games">Back to Gaming Hub</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950">
      <div className="flex h-screen pt-16">
        {/* Sidebar */}
        <div className="w-80 bg-slate-900/95 border-r border-slate-800 flex flex-col">
          {/* Server Header */}
          <div className="p-6 border-b border-slate-800 bg-slate-900/80">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                <Crown className="h-6 w-6 text-orange-400" />
              </div>
              <div>
                <h2 className="font-bold text-xl text-white">
                  JQ Gaming Universe
                </h2>
                <p className="text-sm text-gray-400">Epic Community Hub</p>
              </div>
            </div>

            {/* Community Stats */}
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-slate-800/60 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-cyan-400">{communityStats.totalMembers.toLocaleString()}</div>
                <div className="text-xs text-gray-500">Total Legends</div>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-green-400">{communityStats.onlineNow}</div>
                <div className="text-xs text-gray-500">Online Now</div>
              </div>
            </div>
          </div>

          {/* Channels */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Text Channels */}
            <div className="space-y-1 mb-6">
              <h3 className="text-xs font-bold text-orange-400 uppercase tracking-wide mb-3 flex items-center">
                <Hash className="w-3 h-3 mr-1" />
                Text Channels
              </h3>
              {getChannelsByCategory('Text Channels').map((channel) => (
                <div
                  key={channel.id}
                  onClick={() => setActiveChannel(channel.id)}
                  className={`flex items-center justify-between p-3 rounded-xl cursor-pointer transition-all duration-300 group ${activeChannel === channel.id
                    ? 'bg-cyan-500/20 border border-cyan-500/30 text-cyan-400'
                    : 'text-gray-400 hover:bg-slate-800/60 hover:text-gray-200'
                    }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{channel.icon}</span>
                    <div>
                      <span className="text-sm font-medium">{channel.name.replace('# ', '')}</span>
                      <div className="text-xs text-gray-500">{channel.members.toLocaleString()} members</div>
                    </div>
                  </div>
                  {channel.only_admin && (
                    <Crown className="w-4 h-4 text-orange-400" />
                  )}
                </div>
              ))}
            </div>

            {/* Announcements Channels */}
            <div className="space-y-1 mb-6">
              <h3 className="text-xs font-bold text-orange-400 uppercase tracking-wide mb-3 flex items-center">
                <Hash className="w-3 h-3 mr-1" />
                Announcements Channels
              </h3>
              {getChannelsByCategory('Announcement Channels').map((channel) => (
                <div
                  key={channel.id}
                  onClick={() => setActiveChannel(channel.id)}
                  className={`flex items-center justify-between p-3 rounded-xl cursor-pointer transition-all duration-300 group ${activeChannel === channel.id
                    ? 'bg-cyan-500/20 border border-cyan-500/30 text-cyan-400'
                    : 'text-gray-400 hover:bg-slate-800/60 hover:text-gray-200'
                    }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{channel.icon}</span>
                    <div>
                      <span className="text-sm font-medium">{channel.name.replace('# ', '')}</span>
                      <div className="text-xs text-gray-500">{channel.members.toLocaleString()} members</div>
                    </div>
                  </div>
                  {channel.only_admin && (
                    <Crown className="w-4 h-4 text-orange-400" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col bg-slate-900/95">
          {/* Chat Header */}
          <div className="bg-slate-900/95 border-b border-slate-800 p-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-2xl">{channels.find(c => c.id === activeChannel)?.icon}</div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  #{channels.find(c => c.id === activeChannel)?.name.replace('# ', '') || activeChannel}
                </h1>
                <div className="text-gray-400 text-sm flex items-center gap-2">
                  <span>{channels.find(c => c.id === activeChannel)?.members.toLocaleString()} members</span>
                  <span>•</span>
                  <span className="text-green-400">{channels.find(c => c.id === activeChannel)?.online} online</span>
                  {channels.find(c => c.id === activeChannel)?.only_admin && (
                    <>
                      <span>•</span>
                      <span className="text-orange-400 flex items-center gap-1">
                        <Crown className="w-3 h-3" />
                        Admin Only
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
                <Search className="h-4 w-4 text-gray-400" />
              </button>
              <button className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
                <Filter className="h-4 w-4 text-gray-400" />
              </button>
              {/* Real-time connection status */}
              <div className="flex items-center space-x-2">
                {socketConnected ? (
                  <>
                    <Wifi className="h-4 w-4 text-green-400" />
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  </>
                ) : (
                  <>
                    <WifiOff className="h-4 w-4 text-red-400" />
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Messages */}
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto p-6 space-y-6 bg-slate-900/50"
          >
            {messagesLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-gray-400 flex items-center gap-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-400"></div>
                  Loading messages...
                </div>
              </div>
            ) : messagesError ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-red-400 text-center">
                  <p>Error loading messages</p>
                  <button
                    onClick={() => refetchMessages()}
                    className="mt-2 text-sm text-cyan-400 hover:text-cyan-300"
                  >
                    Try again
                  </button>
                </div>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-gray-400 text-center">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No messages yet in this channel</p>
                  <p className="text-sm">Be the first to start the conversation!</p>
                </div>
              </div>
            ) : (
              messages.map((msg) => (
                <div key={msg.id} className={`group relative ${msg.pinned ? 'bg-orange-500/10 border border-orange-500/30 rounded-xl p-4' : ''}`}>
                  {msg.pinned && (
                    <div className="flex items-center space-x-2 mb-3 text-orange-400 text-sm font-medium">
                      <Pin className="h-4 w-4" />
                      <Crown className="h-4 w-4" />
                      <span>Pinned Announcement</span>
                    </div>
                  )}

                  <div className="flex items-start space-x-4">
                    <div className="relative">
                      <div className="w-12 h-12 rounded-xl bg-slate-800 flex items-center justify-center text-xl border border-slate-700">
                        {/* {msg.avatar} */}
                      </div>
                      {msg.staff && (
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                          <Crown className="w-3 h-3 text-slate-900" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`font-bold text-lg ${msg.staff ? 'text-orange-400' : 'text-gray-200'}`}>
                            {msg.user}
                          </span>

                          {/* {msg.level && (
                          <span className="text-xs bg-slate-800/60 text-cyan-400 px-2 py-1 rounded-full font-medium">
                            Lv.{msg.level}
                          </span>
                        )} */}

                          {msg.staff && (
                            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-slate-900 text-xs px-3 py-1 font-bold rounded-full flex items-center">
                              <Crown className="w-3 h-3 mr-1" />
                              STAFF
                            </div>
                          )}

                          {/* {msg.badges && (
                          <div className="flex space-x-1">
                            {msg.badges.map((badge, index) => (
                              <span key={index} className="text-sm" title="Achievement Badge">
                                {badge}
                              </span>
                            ))}
                          </div>
                        )} */}

                          <span className="text-xs text-gray-500">{msg.timestamp}</span>
                        </div>

                        {/* Delete button - only show if user can delete */}
                        {/* {canDeleteMessage(msg) && (
                        <button
                          onClick={() => handleDeleteMessage(msg.message_id)}
                          className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-500/20 hover:text-red-400 rounded transition-all duration-200"
                          disabled={deleteMessageMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )} */}
                      </div>

                      <p className="text-gray-300 mb-3 leading-relaxed break-words">{msg.message}</p>

                      <div className="flex flex-wrap gap-2 mt-3">
                        {/* Existing Reactions */}
                        {msg.reactions.map((reaction, index) => (
                          <button
                            key={index}
                            onClick={() => handleReaction(msg.message_id, reaction.emoji)}
                            disabled={addReactionMutation.isPending}
                            className="flex items-center space-x-2 bg-slate-800/60 border border-slate-700 rounded-full px-3 py-1 text-sm hover:bg-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300 disabled:opacity-50"
                          >
                            <span className="text-base">{reaction.emoji}</span>
                            <span className="font-medium text-gray-300">{reaction.count}</span>
                          </button>
                        ))}

                        {/* Add Reaction Button - Always visible if user can react */}
                        {userData && (
                          <div className="relative group">
                            <button
                              className={`flex items-center justify-center w-8 h-8 bg-slate-800/60 border border-slate-700 rounded-full text-sm hover:bg-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300 ${msg.reactions.length === 0 ? 'opacity-70 group-hover:opacity-100' : 'opacity-0 group-hover:opacity-100'
                                }`}
                              disabled={addReactionMutation.isPending}
                              onClick={() => {
                                // Show quick emoji picker or default to 👍
                                handleReaction(msg.message_id, '👍');
                              }}
                            >
                              {addReactionMutation.isPending ? (
                                <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-400"></div>
                              ) : (
                                <Smile className="h-4 w-4 text-gray-400" />
                              )}
                            </button>

                            {/* Quick Emoji Picker on Hover */}
                            <div className="absolute bottom-full left-0 mb-2 opacity-0 group-hover:opacity-100 pointer-events-none group-hover:pointer-events-auto transition-all duration-200 z-10">
                              <div className="bg-slate-800 border border-slate-700 rounded-xl p-2 shadow-2xl">
                                <div className="flex space-x-1">
                                  {['👍', '❤️', '😂', '😮', '😢', '😡', '🎉', '🔥', '🚀', '💯'].map((emoji) => (
                                    <button
                                      key={emoji}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleReaction(msg.message_id, emoji);
                                      }}
                                      disabled={addReactionMutation.isPending}
                                      className="w-8 h-8 flex items-center justify-center hover:bg-slate-700 rounded-lg transition-colors text-lg disabled:opacity-50"
                                      title={`React with ${emoji}`}
                                    >
                                      {emoji}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* No reactions state - encourage interaction */}
                        {msg.reactions.length === 0 && userData && (
                          <div className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-2">
                            <span>Be the first to react</span>
                            <div className="flex space-x-1">
                              {['👍', '❤️', '🎉'].map((emoji) => (
                                <button
                                  key={emoji}
                                  onClick={() => handleReaction(msg.message_id, emoji)}
                                  disabled={addReactionMutation.isPending}
                                  className="hover:scale-110 transition-transform disabled:opacity-50"
                                  title={`React with ${emoji}`}
                                >
                                  {emoji}
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}

            {/* Loading indicators for mutations */}
            {sendMessageMutation.isPending && (
              <div className="text-center text-gray-400">
                <div className="inline-flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
                  Sending message...
                </div>
              </div>
            )}
            {/* </div> */}


            {/* Loading indicators */}
            {sendMessageMutation.isPending && (
              <div className="text-center text-gray-400">
                <div className="inline-flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
                  Sending message...
                </div>
              </div>
            )}

            {/* Scroll to bottom button */}
            {!isAtBottom() && (
              <div className="fixed bottom-32 right-8 z-10">
                <button
                  onClick={() => scrollToBottom(true)}
                  className="bg-cyan-500 hover:bg-cyan-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
                  title="Scroll to bottom"
                >
                  <MessageCircle className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>

          {/* Message Input */}
          <div className="p-6 border-t border-slate-800 bg-slate-900/95">
            {(() => {
              // Get current channel data
              const currentChannel = channels.find(c => c.id === activeChannel);

              // Check if user is connected
              if (!userData) {
                return (
                  <div className="text-center p-8 bg-slate-900/90 border border-slate-800 rounded-2xl">
                    <div className="w-16 h-16 bg-cyan-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MessageCircle className="h-8 w-8 text-cyan-400" />
                    </div>
                    <h3 className="text-2xl font-bold mb-2 text-white">
                      Join the Epic Conversation
                    </h3>
                    <p className="text-gray-400 mb-6 max-w-md mx-auto leading-relaxed">
                      Connect your wallet to participate in community discussions, earn social points, and become part of the gaming legend.
                    </p>
                    <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-orange-400" />
                        <span>Earn Social Points</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Trophy className="w-4 h-4 text-cyan-400" />
                        <span>Join Tournaments</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Crown className="w-4 h-4 text-purple-400" />
                        <span>Build Reputation</span>
                      </div>
                    </div>
                    <button
                      onClick={() => setShowAccountConnectModal(true)}
                      className="mt-6 bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300"
                    >
                      Connect Wallet
                    </button>
                  </div>
                );
              }

              // Check channel restrictions
              if (currentChannel?.only_admin && !userData.is_admin) {
                return (
                  <div className="text-center p-6 bg-orange-500/10 border border-orange-500/30 rounded-2xl">
                    <div className="w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Crown className="h-6 w-6 text-orange-400" />
                    </div>
                    <h3 className="text-lg font-bold mb-2 text-white">
                      Admin Only Channel
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed">
                      Only administrators can send messages in this channel. You can still view messages and reactions.
                    </p>
                  </div>
                );
              }

              if (currentChannel?.only_moderators && !userData.is_moderator && !userData.is_admin) {
                return (
                  <div className="text-center p-6 bg-purple-500/10 border border-purple-500/30 rounded-2xl">
                    <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Shield className="h-6 w-6 text-purple-400" />
                    </div>
                    <h3 className="text-lg font-bold mb-2 text-white">
                      Moderator Only Channel
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed">
                      Only moderators and administrators can send messages in this channel. You can still view messages and reactions.
                    </p>
                  </div>
                );
              }

              // User can send messages
              return (
                <div className="space-y-3">
                  {/* User info banner */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-slate-800 flex items-center justify-center border border-slate-700">
                        <span className="text-xs font-medium">
                          {userData.in_game_name?.[0] || userData.first_name[0]}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-300 font-medium">
                          {userData.in_game_name || `${userData.first_name} ${userData.last_name}`}
                        </span>
                        {userData.is_admin && (
                          <div className="bg-orange-500 text-slate-900 text-xs px-2 py-0.5 font-bold rounded-full flex items-center">
                            <Crown className="w-3 h-3 mr-1" />
                            ADMIN
                          </div>
                        )}
                        {userData.is_moderator && !userData.is_admin && (
                          <div className="bg-purple-500 text-white text-xs px-2 py-0.5 font-bold rounded-full flex items-center">
                            <Shield className="w-3 h-3 mr-1" />
                            MOD
                          </div>
                        )}
                        {/* {userData.social_points && (
                          <span className="text-cyan-400 text-xs">
                            {userData.social_points.toLocaleString()} SP
                          </span>
                        )} */}
                      </div>
                    </div>
                  </div>

                  {/* Message input */}
                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <textarea
                        placeholder={`Share your epic gaming moments in #${currentChannel?.name?.replace('# ', '') || activeChannel}...`}
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                        disabled={sendMessageMutation.isPending}
                        className="w-full resize-none bg-slate-800/60 border border-slate-700 focus:border-cyan-500 max-h-32 rounded-xl text-gray-200 placeholder:text-gray-500 p-3 outline-none transition-colors disabled:opacity-50"
                        rows={1}
                      />
                    </div>
                    <button
                      onClick={handleSendMessage}
                      disabled={!message.trim() || sendMessageMutation.isPending}
                      className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-bold p-3 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[48px]"
                    >
                      {sendMessageMutation.isPending ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      ) : (
                        <Send className="h-5 w-5" />
                      )}
                    </button>
                  </div>

                  {/* Channel info */}
                  {currentChannel && (
                    <div className="text-xs text-gray-500 flex items-center space-x-4">
                      {currentChannel.only_admin && (
                        <div className="flex items-center space-x-1">
                          <Crown className="w-3 h-3 text-orange-400" />
                          <span>Admin permissions granted</span>
                        </div>
                      )}
                      {currentChannel.only_moderators && !currentChannel.only_admin && (
                        <div className="flex items-center space-x-1">
                          <Shield className="w-3 h-3 text-purple-400" />
                          <span>Moderator permissions granted</span>
                        </div>
                      )}
                      <span>Press Enter to send • Shift+Enter for new line</span>
                    </div>
                  )}
                </div>
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesktopCommunity;
