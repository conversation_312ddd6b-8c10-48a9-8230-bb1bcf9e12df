import { <PERSON> } from "react-router-dom";
import { Play, Users, Coins, Star, Target } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useGame } from "@/contexts/GameContext";
import { gameUrls } from "@/game-data";

interface MobileGameCardProps {
  game: {
    id: string;
    gameId: string;
    title: string;
    description: string;
    image: string;
    blockchain: string;
    rewards: string;
    difficulty: string;
    status: string;
    gradient: string;
    borderGlow: string;
    gameUrl?: string;
  };
  onGameSelect: (game: any) => void;
  index: number;
}

const MobileGameCard = ({ game, onGameSelect, index }: MobileGameCardProps) => {
  const { userData, openAccountConnectModal, isAuthLoading } = useGame();

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      Easy: "bg-success-green/20 text-success-green border-success-green/30",
      Medium: "bg-legendary-orange/20 text-legendary-orange border-legendary-orange/30",
      Hard: "bg-health-red/20 text-health-red border-health-red/30",
    };
    return colors[difficulty as keyof typeof colors] || "";
  };

  const getBlockchainColor = (blockchain: string) => {
    const colors = {
      BSC: "bg-binance-gold text-deep-space",
      SOL: "bg-solana-purple text-white",
      ETH: "bg-ethereum-blue text-white",
      AVAX: "bg-red-500 text-white",
      MATIC: "bg-purple-500 text-white",
    };
    return colors[blockchain as keyof typeof colors] || "bg-accent-cyan text-white";
  };

  return (
    <div
      className={`group relative bg-gradient-to-br ${game.gradient} backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 transition-all duration-500 hover:shadow-2xl overflow-hidden`}
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      {/* Status badge - Mobile optimized positioning */}
      <div className="absolute top-4 right-4 z-20">
        <span
          className={`px-2 py-1 text-xs font-bold rounded-full ${
            game.status === "Live"
              ? "bg-success-green text-white"
              : "bg-warning-orange text-deep-space"
          }`}
        >
          {game.status}
        </span>
      </div>

      <div className="relative z-10">
        {/* Game icon - Mobile optimized size */}
        <div className="text-center mb-6">
          <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
            {game.image}
          </div>
        </div>

        {/* Game info - Mobile optimized layout */}
        <div className="space-y-3 mb-6">
          <div className="flex items-start justify-between">
            <h3 className="text-xl font-bold group-hover:text-white transition-colors duration-300 flex-1 pr-2">
              {game.title}
            </h3>
            <span
              className={`${getBlockchainColor(game.blockchain)} px-2 py-1 rounded-full text-xs font-medium flex-shrink-0`}
            >
              {game.blockchain}
            </span>
          </div>

          <p className="text-secondary-text group-hover:text-primary-text transition-colors duration-300 leading-relaxed text-sm line-clamp-3">
            {game.description}
          </p>
        </div>

        {/* Badges - Mobile optimized spacing */}
        <div className="flex flex-wrap gap-2 mb-6">
          <Badge
            className={`${getDifficultyColor(game.difficulty)} border text-xs`}
          >
            {game.difficulty}
          </Badge>
          <Badge className="bg-xp-purple/20 text-xp-purple border-xp-purple/30 text-xs">
            Action
          </Badge>
        </div>

        {/* Stats grid - Mobile optimized 2x2 layout */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          <div className="bg-card-dark/60 rounded-xl p-3 group-hover:bg-card-dark/80 transition-colors duration-300">
            <div className="flex items-center space-x-2">
              <Coins className="h-4 w-4 text-gaming-gold flex-shrink-0" />
              <div className="min-w-0">
                <div className="text-xs text-secondary-text">Rewards</div>
                <div className="font-bold text-gaming-gold text-sm truncate">
                  {game.rewards}
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-card-dark/60 rounded-xl p-3 group-hover:bg-card-dark/80 transition-colors duration-300">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-accent-cyan flex-shrink-0" />
              <div className="min-w-0">
                <div className="text-xs text-secondary-text">Players</div>
                <div className="font-bold text-accent-cyan text-sm truncate">
                  12.5K
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-card-dark/60 rounded-xl p-3 group-hover:bg-card-dark/80 transition-colors duration-300">
            <div className="flex items-center space-x-2">
              <Star className="h-4 w-4 text-xp-purple flex-shrink-0" />
              <div className="min-w-0">
                <div className="text-xs text-secondary-text">Difficulty</div>
                <div className="font-bold text-xp-purple text-sm truncate">
                  {game.difficulty}
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-card-dark/60 rounded-xl p-3 group-hover:bg-card-dark/80 transition-colors duration-300">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-success-green flex-shrink-0" />
              <div className="min-w-0">
                <div className="text-xs text-secondary-text">Status</div>
                <div className="font-bold text-success-green text-sm truncate">
                  {game.status}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Button - Mobile optimized touch target */}
        {userData ? (
          <Button
            asChild
            className="w-full group/btn relative overflow-hidden bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold py-4 rounded-xl hover:scale-105 transition-all duration-300 h-14"
          >
            <Link
              to={`/game/${game.gameId}`}
              onClick={() => onGameSelect(game)}
            >
              <span className="flex items-center justify-center gap-3">
                <Play className="w-5 h-5 group-hover/btn:scale-110 transition-transform duration-300" />
                Enter Arena
                <Target className="w-5 h-5 group-hover/btn:rotate-180 transition-transform duration-500" />
              </span>
            </Link>
          </Button>
        ) : (
          <Button
            onClick={openAccountConnectModal}
            disabled={isAuthLoading}
            className="w-full bg-gradient-to-r from-surface-dark to-border-light text-primary-text font-bold hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed h-14"
          >
            {isAuthLoading ? (
              <>
                <div className="w-5 h-5 border-2 border-accent-cyan border-t-transparent rounded-full animate-spin mr-2" />
                Connecting...
              </>
            ) : (
              <>
                <Target className="w-5 h-5 mr-2" />
                Connect to Play
              </>
            )}
          </Button>
        )}
      </div>

      {/* Enhanced holographic overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/10 via-transparent to-xp-purple/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none"></div>
    </div>
  );
};

export default MobileGameCard;
