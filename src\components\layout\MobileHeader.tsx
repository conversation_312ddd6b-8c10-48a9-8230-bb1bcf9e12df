import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  Gamepad2,
  Menu,
  X,
  Wallet,
  LogOut,
  Settings,
  User,
  Zap,
  Sparkles,
  Loader2,
  Home,
  Users,
  Trophy,
} from "lucide-react";
import { useWallet } from "@/contexts/WalletContext";
import { useGame } from "@/contexts/GameContext";

const MobileHeader = () => {
  const location = useLocation();
  const { isConnected, address, connectWallet, disconnectWallet } = useWallet();
  const { openAccountConnectModal, userData, isAuthLoading } = useGame();

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "Games", href: "/games", icon: Trophy },
    { name: "Community", href: "/community", icon: Users },
  ];

  const isActive = (path: string) => location.pathname === path;

  const formatWalletAddress = (address: string | null) => {
    if (!address) return "";
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-slate-900/95 backdrop-blur-lg border-b border-slate-800">
      {/* Main Header Bar */}
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo - Simplified for mobile */}
          <Link to="/" className="flex items-center space-x-2 group">
            <div className="relative">
              <div className="w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center hover:scale-110 transition-all duration-300">
                <Gamepad2 className="h-5 w-5 text-cyan-400" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
            </div>
            <span className="text-lg font-bold text-white">JargonQuest</span>
          </Link>

          {/* Right side actions */}
          <div className="flex items-center space-x-3">
            {/* Wallet Connection - Mobile optimized */}
            {userData ? (
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center justify-center space-x-2 bg-slate-800/60 border border-slate-700 hover:border-cyan-500/50 transition-all duration-300 w-12 h-10 rounded-lg"
              >
                <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-cyan-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="h-3 w-3 text-white" />
                </div>
              </button>
            ) : (
              <button
                onClick={openAccountConnectModal}
                disabled={isAuthLoading}
                className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-medium transition-all duration-300 w-12 h-10 flex items-center justify-center rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isAuthLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <User className="h-4 w-4" />
                )}
              </button>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-slate-900/95 backdrop-blur-lg border-b border-slate-800">
          <div className="px-4 py-4 space-y-3">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                onClick={() => setIsMenuOpen(false)}
                className={`flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-300 ${
                  isActive(item.href)
                    ? "text-white bg-gradient-to-r from-cyan-500/20 to-cyan-600/20 border border-cyan-500/30"
                    : "text-gray-400 hover:text-white hover:bg-slate-800/50"
                }`}
              >
                <item.icon className="h-5 w-5" />
                <span className="font-medium text-base">{item.name}</span>
                {isActive(item.href) && (
                  <div className="ml-auto w-2 h-2 bg-cyan-400 rounded-full"></div>
                )}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Mobile Profile Dropdown */}
      {isProfileOpen && (
        <div className="md:hidden absolute top-full right-0 mt-2 w-64 bg-slate-900 border border-slate-800 rounded-lg shadow-xl z-50">
          <div className="p-4 border-b border-slate-800">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-cyan-500 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  Connected
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {formatWalletAddress(address)}
                </p>
              </div>
            </div>
          </div>

          <div className="p-2">
            <Link
              to="/profile"
              className="flex items-center space-x-3 px-3 py-3 rounded-lg hover:bg-slate-800 transition-colors text-gray-300 hover:text-white"
              onClick={() => setIsProfileOpen(false)}
            >
              <User className="h-4 w-4" />
              <span>Profile</span>
            </Link>
            <button
              onClick={() => {
                setIsProfileOpen(false);
              }}
              className="w-full flex items-center space-x-3 px-3 py-3 rounded-lg hover:bg-red-500/20 text-red-400 transition-colors"
            >
              <LogOut className="h-4 w-4" />
              <span>Disconnect</span>
            </button>
          </div>
        </div>
      )}

      {/* Background overlay when profile is open */}
      {isProfileOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsProfileOpen(false)}
        />
      )}
    </header>
  );
};

export default MobileHeader;
