<!DOCTYPE html>
<html>

<head>
    <title>Fifteen puzzle maker</title>
    <link rel="shortcut icon" href="icon.jpg" type="image/x-icon">
    <meta content="#c4cebb" name="theme-color" charset="UTF-8">
    <meta name="description"
        content="A simple implementation of the classic mini-game Fifteen Sliding Puzzle, using HTML5 DOM document elements and without using Canvas or third party libraries.">
    <meta name="keywords" content="15Puzzle, js, Open Source, HTML5, Sliding, web, base">
    <meta name="author" content="Kirill Live">
    <style>
        body {
            height: 100vh;
            padding: 0;
            display: grid;
            align-content: center;
            justify-content: center;
            background-color: #dfebd5;
        }

        * {
            font-family: Arial;
            font-size: 12px;
            border-collapse: collapse;
            border: none;
            margin: 0;
            padding: 0;
            border-spacing: 0px;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            outline: none;
        }

        .input_text {
            border: 0;
            font-family: Arial;
            font-size: 12px;
            -webkit-appearance: none;
            appearance: none;
            border-bottom: 1px solid #c4cebb;
            background-color: transparent;
            cursor: text;
            height: 28px;
            width: 100%;
            -webkit-touch-callout: text;
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        .button {
            border-radius: 8px;
            width: 100%;
            height: 32px;
            background-color: #c4cebb;
            display: grid;
            align-content: center;
            justify-content: center;
            cursor: pointer;
        }

        .button:hover {
            background-color: #dfebd5;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #c4cebb;
            transition: .2s;
            border-radius: 12px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: #fff;
            transition: .2s;
            border-radius: 12px;
        }

        input:checked+.slider {
            background-color: #dfebd5;
        }

        input:checked+.slider:before {
            transform: translateX(26px);
        }
        
        /* Rank badge styling */
        .rank-badge {
            position: fixed;
            top: 12px;
            right: 12px;
            width: 64px;
            height: 64px;
            border-radius: 8px;
            background: rgba(255,255,255,0.6);
            backdrop-filter: blur(2px);
            display: none;
            object-fit: contain;
            z-index: 1000;
        }
        /* Rank color overlay */
        .rank-overlay {
            position: fixed;
            inset: 0;
            pointer-events: none;
            z-index: 0;
            background: transparent;
            opacity: 0.75;
        }
    </style>
</head>

<body>
    <div id="rankOverlay" class="rank-overlay"></div>
    <img id="rankBadge" class="rank-badge" alt="Rank" />
    <div id='fifteen'></div> <!--element "fifteen" in which the game will take place-->
    
    <script>
        // console.log("i am in game", window.userData);
        function waitForUserData(callback, maxAttempts = 50) {
            let attempts = 0;

            function checkUserData() {
                if (window.userData) {
                    console.log("userData received:", window.userData);
                    callback(window.userData);
                } else if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(checkUserData, 100); // Check every 100ms
                } else {
                    console.log("userData not received after", maxAttempts * 100, "ms");
                    // Initialize game with default values or show error
                    callback(null);
                }
            }

            checkUserData();
        }
        // Lightweight ranks config (mirrors src/ranks-data/index.js thresholds/colors)
        const RANKS = [
            { name: 'Jargon Jedi', from: 0, to: 4999, colors: { background: '#b4734bbf', boxshadow: '-10px -10px 45px 25px #64411D, 10px 10px 162px 44px #A36135' }, image: 'Rank 1. - Jargon Jedi.png' },
            { name: 'Nomenclature Ninja', from: 5000, to: 24999, colors: { background: '#738596bf', boxshadow: '-10px -10px 45px 25px #405059, 10px 10px 162px 44px #748B9C' }, image: 'Rank 2. - Nomenclature Ninja.png' },
            { name: 'Slang Sorcerer', from: 25000, to: 99999, colors: { background: '#d9a045cc', boxshadow: '-10px -10px 45px 25px #A66E29, 10px 10px 162px 44px #D29F47' }, image: 'Rank 3. - Slang Sorcerer .png' },
            { name: 'Jargon Surgeon', from: 100000, to: 999999, colors: { background: '#738596bf', boxshadow: '-10px -10px 45px 25px #405059, 10px 10px 162px 44px #748B9C' }, image: 'Rank 4. - Jargon Surgeon .png' },
            { name: 'Jargon Explorer', from: 1000000, to: 1999999, colors: { background: '#3c83f6bf', boxshadow: '-10px -10px 45px 25px #1e3b8a, 10px 10px 162px 44px #3c83f6' }, image: 'Rank 5. - Jargon Explorer.png' },
            { name: 'Terminology Trickster', from: 2000000, to: 9999999, colors: { background: '#9864d8bf', boxshadow: '-10px -10px 45px 25px #7C45A7, 10px 10px 162px 44px #7B67D2' }, image: 'Rank 6. - Terminology Trickster.png' },
            { name: 'Acronym Ace', from: 10000000, to: 49999999, colors: { background: '#6ab4abbf', boxshadow: '-10px -10px 45px 25px #0E5944, 10px 10px 162px 44px #1D9581' }, image: 'Rank 7. - Acronym Ace .png' },
            { name: 'Word Wielder', from: 50000000, to: 99999999, colors: { background: '#2b85c5bf', boxshadow: '-10px -10px 45px 25px #2A6BBF, 10px 10px 162px 44px #2B85C4' }, image: 'Rank 8. - Word Wielder .png' },
            { name: 'Etymology Expert', from: 100000000, to: 999999999, colors: { background: '#4556d9bf', boxshadow: '-10px -10px 45px 25px #1B2C8C, 10px 10px 162px 44px #2F40D5' }, image: 'Rank 9. - Etymology Expert .png' },
            { name: 'Proverb Prodigy', from: 1000000000, to: 17999999999, colors: { background: '#d2beacba', boxshadow: '-10px -10px 45px 25px #81776cc9, 10px 10px 162px 44px #f4af72' }, image: 'Rank 10. - Proverb Prodigy .png' },
            { name: 'Jargon Juggler', from: 18000000000, to: Number.POSITIVE_INFINITY, colors: { background: '#94acc2bf', boxshadow: '-10px -10px 45px 25px #274f71a6, 10px 10px 162px 44px #BDABA8' }, image: 'Rank 11. - Jargon Juggler .png' },
        ];

        function getRankByPoints(totalPoints) {
            for (let i = 0; i < RANKS.length; i++) {
                const r = RANKS[i];
                if (totalPoints >= r.from && totalPoints <= r.to) return r;
            }
            return RANKS[0];
        }

        function getRankIndexByPoints(totalPoints) {
            for (let i = 0; i < RANKS.length; i++) {
                const r = RANKS[i];
                if (totalPoints >= r.from && totalPoints <= r.to) return i;
            }
            return 0;
        }

        var engineLoaded = false;
        function loadEngineOnce() {
            if (engineLoaded) return;
            var s = document.createElement('script');
            s.src = 'fifteen_puzzle.js';
            s.onload = function () { engineLoaded = true; };
            document.body.appendChild(s);
        }

        function setPuzzleArtFromUrl(url, onReady) {
            // Update the puzzle image to the provided URL and recalc layout
            setup.puzzle_fifteen.art.url = url;
            const tempImg = new Image();
            tempImg.onload = function () {
                setup.puzzle_fifteen.size = [tempImg.width, tempImg.height];
                auto_grid();
                auto_style();
                if (typeof onReady === 'function') { onReady(); }
                if (engineLoaded && typeof fifteen_update === 'function') { fifteen_update(); }
            };
            tempImg.src = url;
        }

        function trySetRankBadge(imageFileName) {
            // Try common public locations where the image may be available
            const candidates = [
                '/assets/ranks/' + imageFileName, // if copied to public assets at build
                '../../../../../src/assets/ranks/' + imageFileName, // dev fallback (may 404)
            ];
            const badge = document.getElementById('rankBadge');
            let idx = 0;
            function tryNext() {
                if (idx >= candidates.length) { badge.style.display = 'none'; return; }
                const url = candidates[idx++];
                badge.onerror = tryNext;
                badge.onload = function () {
                    badge.style.display = 'block';
                    // Make the puzzle use the same image as the badge
                    setPuzzleArtFromUrl(badge.src, function(){
                        // Load the engine only after ranks and image are set
                        loadEngineOnce();
                    });
                };
                badge.src = url;
            }
            tryNext();
        }

        function applyRankStyling(rank) {
            // Body background color
            document.body.style.backgroundColor = "#5b5b5c"; // dark base
            // Ensure no box-shadow is applied to tiles
            if (setup.puzzle_fifteen.style.includes('box-shadow')) {
                setup.puzzle_fifteen.style = setup.puzzle_fifteen.style.replace(/;?box-shadow:[^;]*/g, '');
            }
            // Apply subtle rank color overlay
            var overlay = document.getElementById('rankOverlay');
            if (overlay) {
                overlay.style.backgroundColor = rank.colors.background;
                overlay.style.opacity = '0.28';
            }
            // Badge image (best-effort)
            trySetRankBadge(rank.image);
        }

        // Difficulty mapping per rank index
        const DIFFICULTY_BY_INDEX = [
            { grid: [3, 4], diff: 200 },
            { grid: [3, 4], diff: 300 },
            { grid: [4, 4], diff: 350 },
            { grid: [4, 5], diff: 400 },
            { grid: [5, 5], diff: 450 },
            { grid: [5, 6], diff: 500 },
            { grid: [6, 6], diff: 600 },
            { grid: [6, 6], diff: 700 },
            { grid: [6, 6], diff: 800 },
            { grid: [6, 6], diff: 900 },
            { grid: [6, 6], diff: 1000 },
        ];

        function applyRankDifficulty(rankIndex) {
            const config = DIFFICULTY_BY_INDEX[Math.min(rankIndex, DIFFICULTY_BY_INDEX.length - 1)];
            if (!config) return;
            setup.puzzle_fifteen.diff = config.diff;
            setup.puzzle_fifteen.grid = config.grid.slice();
            // Prevent auto_grid from overriding the chosen grid
            setup.puzzle_fifteen.lockedGrid = true;
            // Rendering is triggered after the rank image loads
        }

        waitForUserData(function (userData) {
            if (userData) {
                console.log("Game initializing with user data:", userData);
                // Rank-driven theming
                const totalPoints = Number(userData || 0);
                console.log("totalPoints",totalPoints)
                const rank = getRankByPoints(totalPoints);
                console.log("rankssss",rank)
                const rankIndex = getRankIndexByPoints(totalPoints);
                applyRankStyling(rank);
                applyRankDifficulty(rankIndex);
            } else {
                console.log("Game initializing with default settings");
            }
        });
        var setup = {
            puzzle_fifteen: {
                diff: 300, // number of movements of the slots for shuffling pictures
                size: [512, 640], // element size "fifteen" in pixels only
                grid: [3, 4], // the number of squares in the height and width of the picture
                fill: true, // Stretching the area with the game to fit the element is recommended for fullscreen
                number: false, // Slot sequence number
                art: {
                    url: "art.jpg", // path to the picture (you can use any format of supported browsers, gif-animation of svg)
                    ratio: false // enlarge the picture in height or width
                },
                // optional elements
                keyBoard: true, // Control using the keys on the keyboard
                gamePad: true, // Control using the joystick on the Gamepad
                time: 0.1, // block move animation time
                style: "background-color:#c4cebb;display:grid;justify-items:center;align-items:center;font-family:Arial;color:#fff;border-radius:12px;font-size:32px;" // style for puzzle square
            }
        }
        // Removed legacy slot_style and upload controls to simplify the embedded build
        function auto_grid() {
            let s = setup.puzzle_fifteen
            if (s.lockedGrid) { return; }
            if (s.size[1] < s.size[0]) { s.grid = [Math.round(s.size[0] / (s.size[1] / 4)) - 1, 3] }
            else { s.grid = [3, Math.round(s.size[1] / (s.size[0] / 4)) - 1] }
        }
        function auto_style() {
            let s = setup.puzzle_fifteen, v, i;
            if (s.size[1] < s.size[0]) { v = Math.round((s.size[0] / s.grid[0]) / 16) }
            else { v = Math.round((s.size[1] / s.grid[1]) / 16) }
            d = s.style.split(";");
            for (i = 0; i < d.length; i++) {
                if (d[i].includes("border-radius")) { s.style = s.style.replace(d[i], "border-radius:" + Math.round(v * 1.35) + "px") }
                else if (d[i].includes("font-size")) { s.style = s.style.replace(d[i], "font-size:" + (v * 3) + "px") }
            }
            if (typeof slot_style !== 'undefined' && slot_style) {
                slot_style.value = s.style;
            }
        }
        function fifteen_update() {
            f.innerHTML = "";
            ceation_slots();
        }
        // Removed legacy HTML builder and drag/drop upload features
    </script>
    <!-- Engine is dynamically loaded after rank image is ready -->
</body>

</html>