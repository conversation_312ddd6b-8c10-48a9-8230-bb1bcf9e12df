import rank0 from '../assets/ranks/Rank 1. - Jargon Jedi.png';
import rank1 from '../assets/ranks/Rank 2. - Nomenclature Ninja.png';
import rank2 from '../assets/ranks/Rank 3. - Slang Sorcerer .png';
import rank3 from '../assets/ranks/Rank 4. - Jargon Surgeon .png';
import rank4 from '../assets/ranks/Rank 5. - Jargon Explorer.png';
import rank5 from '../assets/ranks/Rank 6. - Terminology Trickster.png';
import rank6 from '../assets/ranks/Rank 7. - Acronym Ace .png';
import rank7 from '../assets/ranks/Rank 8. - Word Wielder .png';
import rank8 from '../assets/ranks/Rank 9. - Etymology Expert .png';
import rank9 from '../assets/ranks/Rank 10. - Proverb Prodigy .png';
import rank10 from '../assets/ranks/Rank 11. - Jargon Juggler .png';

export const ranks = [
  {
    index: 0,
    image: rank0,
    id: 'jargonJedi',
    name: '<PERSON><PERSON><PERSON>',
    from: 0,
    to: 4999,
    colors: {
      background: '#b4734bbf',
      boxshadow: '-10px -10px 45px 25px #64411D, 10px 10px 162px 44px #A36135',
    },
  },
  {
    index: 1,
    image: rank1,
    id: 'nomenclatureNinja',
    name: 'Nomenclature Ninja',
    from: 5000,
    to: 24999,
    colors: {
      background: '#738596bf',
      boxshadow: '-10px -10px 45px 25px #405059, 10px 10px 162px 44px #748B9C',
    },
  },
  {
    index: 2,
    image: rank2,
    id: 'slangSorcerer',
    name: 'Slang Sorcerer',
    from: 25000,
    to: 99999,
    colors: {
      background: '#d9a045cc',
      boxshadow: '-10px -10px 45px 25px #A66E29, 10px 10px 162px 44px #D29F47',
    },
  },
  {
    index: 3,
    image: rank3,
    id: 'jargonSurgeon',
    name: 'Jargon Surgeon',
    from: 100000,
    to: 999999,
    colors: {
      background: '#738596bf',
      boxshadow: '-10px -10px 45px 25px #405059, 10px 10px 162px 44px #748B9C',
    },
  },
  {
    index: 4,
    image: rank4,
    id: 'jargonExplorer',
    name: 'Jargon Explorer',
    from: 1000000,
    to: 1999999,
    colors: {
      background: '#3c83f6bf',
      boxshadow: '-10px -10px 45px 25px #1e3b8a, 10px 10px 162px 44px #3c83f6',
    },
  },
  {
    index: 5,
    image: rank5,
    id: 'terminologyTrickster',
    name: 'Terminology Trickster',
    from: 2000000,
    to: 9999999,
    colors: {
      background: '#9864d8bf',
      boxshadow: '-10px -10px 45px 25px #7C45A7, 10px 10px 162px 44px #7B67D2',
    },
  },
  {
    index: 6,
    image: rank6,
    id: 'acronymAce',
    name: 'Acronym Ace',
    from: 10000000,
    to: 49999999,
    colors: {
      background: '#6ab4abbf',
      boxshadow: '-10px -10px 45px 25px #0E5944, 10px 10px 162px 44px #1D9581',
    },
  },
  {
    index: 7,
    image: rank7,
    id: 'wordWielder',
    name: 'Word Wielder',
    from: 50000000,
    to: 99999999,
    colors: {
      background: '#2b85c5bf',
      boxshadow: '-10px -10px 45px 25px #2A6BBF, 10px 10px 162px 44px #2B85C4',
    },
  },
  {
    index: 8,
    image: rank8,
    id: 'etymologyExpert',
    name: 'Etymology Expert',
    from: 100000000,
    to: 999999999,
    colors: {
      background: '#4556d9bf',
      boxshadow: '-10px -10px 45px 25px #1B2C8C, 10px 10px 162px 44px #2F40D5',
    },
  },
  {
    index: 9,
    image: rank9,
    id: 'proverbProdigy',
    name: 'Proverb Prodigy',
    from: 1000000000,
    to: 17999999999,
    colors: {
      background: '#d2beacba',
      boxshadow:
        '-10px -10px 45px 25px #81776cc9, 10px 10px 162px 44px #f4af72',
    },
  },
  {
    index: 10,
    image: rank10,
    id: 'jargonJuggler',
    name: 'Jargon Juggler',
    from: 18000000000,
    to: Number.POSITIVE_INFINITY,
    colors: {
      background: '#94acc2bf',
      boxshadow:
        '-10px -10px 45px 25px #274f71a6, 10px 10px 162px 44px #BDABA8',
    },
  },
];