import { useState, useEffect } from "react";
import {
  Send,
  Hash,
  MessageCircle,
  Smile,
  Pin,
  Search,
  Filter,
  Crown,
  Star,
  Trophy,
  Shield,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  Wifi,
  WifiOff,
} from "lucide-react";
import {
  useChannels,
  useSendMessage,
  useAddReaction,
  useChannelMessages,
} from "@/hooks/use-game-api";
import { Channel } from "@/types/api";
import { useGame } from "@/contexts/GameContext";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useRealtimeMessages } from "@/hooks/use-realtime-messages";
import { useSocket } from "@/contexts/SocketContext";

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60)
  );

  if (diffInMinutes < 1) return "Just now";
  if (diffInMinutes < 60) return `${diffInMinutes} mins ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} days ago`;

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) return `${diffInWeeks} weeks ago`;

  return date.toLocaleDateString();
};

const MobileCommunity = () => {
  const [activeChannel, setActiveChannel] = useState("_general");
  const [message, setMessage] = useState("");
  const [showSidebar, setShowSidebar] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<
    Record<string, boolean>
  >({
    "Text Channels": true,
    "Announcement Channels": true,
  });

  const {
    authToken,
    userData,
    showAccountConnectModal,
    setShowAccountConnectModal,
  } = useGame();

  const {
    data: apiChannels,
    isLoading: channelsLoading,
    error: channelsError,
  } = useChannels();
  const {
    data: channelData,
    isLoading: messagesLoading,
    error: messagesError,
    refetch: refetchMessages,
  } = useChannelMessages(activeChannel, authToken || "", {
    enabled: Boolean(authToken && activeChannel && userData),
  });

  // Chat mutations
  const sendMessageMutation = useSendMessage();
  const addReactionMutation = useAddReaction();

  // Socket connection status
  const { isConnected: socketConnected } = useSocket();

  // Real-time messaging
  const { messagesContainerRef, scrollToBottom, isAtBottom } =
    useRealtimeMessages({
      channelId: activeChannel,
      enabled: Boolean(authToken && userData),
      onNewMessage: (newMessage) => {
        console.log("New message received in mobile UI:", newMessage);
      },
      onReactionUpdate: (data) => {
        console.log("Reaction updated in mobile UI:", data);
      },
      onMessageDeleted: (messageId) => {
        console.log("Message deleted in mobile UI:", messageId);
      },
    });

  // Scroll to bottom when channel changes or messages load
  useEffect(() => {
    if (channelData?.messages && channelData.messages.length > 0) {
      setTimeout(() => scrollToBottom(false), 100);
    }
  }, [activeChannel, channelData?.messages, scrollToBottom]);

  const categories = {
    text: "Text Channels",
    voice: "Voice Channels",
    announcement: "Announcement Channels",
  };

  const channels =
    apiChannels?.map((channel: Channel) => ({
      id: channel.channel_id,
      name: `# ${channel.channel_name}`,
      members: channel.member_count,
      online: channel.online_count,
      only_admin: channel.only_admin,
      icon:
        channel.channel_id === "_general"
          ? "💬"
          : channel.channel_id === "_announcements"
          ? "📢"
          : channel.channel_type === "gaming"
          ? "🎮"
          : channel.channel_type === "trading"
          ? "📈"
          : channel.channel_type === "support"
          ? "🛟"
          : "💬",
      only_moderators: channel.only_moderators,
      pinned_messages: channel.pinned_messages,
      category: categories[channel.channel_type],
    })) || [];

  const messages =
    channelData?.messages.map((msg) => ({
      id: msg.message_id,
      message_id: msg.message_id,
      user: msg.sender.username,
      message: msg.content,
      timestamp: formatTimestamp((msg.created_at || new Date()).toString()),
      reactions: msg.reactions,
      pinned: false,
      sender: msg.sender,
      staff: msg.sender.is_admin || msg.sender.is_moderator,
      message_type: msg.message_type,
      reply_to: msg.reply_to,
    })) || [];

  const communityStats = {
    totalMembers: 156789,
    onlineNow: 8,
    messagesLastHour: 1247,
    activeChannels: channels.length,
  };

  const handleSendMessage = async () => {
    if (message.trim() && userData && authToken) {
      try {
        await sendMessageMutation.mutateAsync({
          channelId: activeChannel,
          content: message.trim(),
          authToken: authToken,
          messageType: "text",
        });
        setMessage("");

        // Scroll to bottom after sending message
        setTimeout(() => scrollToBottom(true), 100);

        // Note: No need to refetch messages manually as real-time updates will handle it
      } catch (error) {
        console.error("Failed to send message:", error);
      }
    }
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    if (!userData || !authToken) return;

    try {
      await addReactionMutation.mutateAsync({
        channelId: activeChannel,
        messageId: messageId,
        emoji: emoji,
        authToken: authToken,
      });

      // Note: No need to refetch messages manually as real-time updates will handle it
    } catch (error) {
      console.error("Failed to add reaction:", error);
    }
  };

  const getChannelsByCategory = (category: string) => {
    return channels.filter((channel) => channel.category === category);
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  // Loading states
  if (channelsLoading) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center pt-24">
        <div className="text-white flex items-center gap-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-400"></div>
          Loading channels...
        </div>
      </div>
    );
  }

  if (channelsError) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center pt-24">
        <div className="text-red-400">
          Error loading channels: {channelsError.message}
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen relative overflow-hidden pt-24">
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-8 text-center max-w-md mx-auto">
            <div className="mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-10 w-10 text-accent-cyan" />
              </div>
              <div className="text-4xl mb-3">🎮</div>
            </div>
            <h2 className="text-2xl font-bold mb-3 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Account
            </h2>
            <p className="text-secondary-text mb-4 leading-relaxed text-sm">
              You need to connect your account to participate in community
              discussions.
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold px-6 py-2 hover:scale-105 transition-all duration-300"
            >
              <Link to="/games">Back to Gaming Hub</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950 pt-24">
      {/* Mobile Header */}
      <div className="bg-slate-900/95 border-b border-slate-800 p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowSidebar(!showSidebar)}
            className="p-2 hover:bg-slate-800 rounded-lg transition-colors"
          >
            {showSidebar ? (
              <X className="h-5 w-5 text-gray-400" />
            ) : (
              <Menu className="h-5 w-5 text-gray-400" />
            )}
          </button>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center">
              <Crown className="h-4 w-4 text-orange-400" />
            </div>
            <div>
              <h2 className="font-bold text-lg text-white">JQ Gaming</h2>
              <p className="text-xs text-gray-400">Community Hub</p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
            <Search className="h-4 w-4 text-gray-400" />
          </button>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-120px)]">
        {/* Mobile Sidebar - Collapsible */}
        {showSidebar && (
          <div className="w-80 bg-slate-900/95 border-r border-slate-800 flex flex-col absolute left-0 top-0 h-full z-20">
            {/* Server Header */}
            <div className="p-4 border-b border-slate-800 bg-slate-900/80">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                  <Crown className="h-5 w-5 text-orange-400" />
                </div>
                <div>
                  <h2 className="font-bold text-lg text-white">
                    JQ Gaming Universe
                  </h2>
                  <p className="text-xs text-gray-400">Epic Community Hub</p>
                </div>
              </div>

              {/* Community Stats */}
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-slate-800/60 rounded-lg p-2 text-center">
                  <div className="text-sm font-bold text-cyan-400">
                    {communityStats.totalMembers.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">Total Legends</div>
                </div>
                <div className="bg-slate-800/60 rounded-lg p-2 text-center">
                  <div className="text-sm font-bold text-green-400">
                    {communityStats.onlineNow}
                  </div>
                  <div className="text-xs text-gray-500">Online Now</div>
                </div>
              </div>
            </div>

            {/* Channels */}
            <div className="flex-1 overflow-y-auto p-3">
              {/* Text Channels */}
              <div className="space-y-1 mb-4">
                <button
                  onClick={() => toggleCategory("Text Channels")}
                  className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-slate-800/60 transition-colors"
                >
                  <h3 className="text-xs font-bold text-orange-400 uppercase tracking-wide flex items-center">
                    <Hash className="w-3 h-3 mr-1" />
                    Text Channels
                  </h3>
                  {expandedCategories["Text Channels"] ? (
                    <ChevronUp className="w-3 h-3 text-orange-400" />
                  ) : (
                    <ChevronDown className="w-3 h-3 text-orange-400" />
                  )}
                </button>
                {expandedCategories["Text Channels"] && (
                  <div className="space-y-1 ml-4">
                    {getChannelsByCategory("Text Channels").map((channel) => (
                      <div
                        key={channel.id}
                        onClick={() => {
                          setActiveChannel(channel.id);
                          setShowSidebar(false);
                        }}
                        className={`flex items-center justify-between p-2 rounded-lg cursor-pointer transition-all duration-300 ${
                          activeChannel === channel.id
                            ? "bg-cyan-500/20 border border-cyan-500/30 text-cyan-400"
                            : "text-gray-400 hover:bg-slate-800/60 hover:text-gray-200"
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <span className="text-base">{channel.icon}</span>
                          <div>
                            <span className="text-sm font-medium">
                              {channel.name.replace("# ", "")}
                            </span>
                            <div className="text-xs text-gray-500">
                              {channel.members.toLocaleString()} members
                            </div>
                          </div>
                        </div>
                        {channel.only_admin && (
                          <Crown className="w-3 h-3 text-orange-400" />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Announcements Channels */}
              <div className="space-y-1 mb-4">
                <button
                  onClick={() => toggleCategory("Announcement Channels")}
                  className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-slate-800/60 transition-colors"
                >
                  <h3 className="text-xs font-bold text-orange-400 uppercase tracking-wide flex items-center">
                    <Hash className="w-3 h-3 mr-1" />
                    Announcements
                  </h3>
                  {expandedCategories["Announcement Channels"] ? (
                    <ChevronUp className="w-3 h-3 text-orange-400" />
                  ) : (
                    <ChevronDown className="w-3 h-3 text-orange-400" />
                  )}
                </button>
                {expandedCategories["Announcement Channels"] && (
                  <div className="space-y-1 ml-4">
                    {getChannelsByCategory("Announcement Channels").map(
                      (channel) => (
                        <div
                          key={channel.id}
                          onClick={() => {
                            setActiveChannel(channel.id);
                            setShowSidebar(false);
                          }}
                          className={`flex items-center justify-between p-2 rounded-lg cursor-pointer transition-all duration-300 ${
                            activeChannel === channel.id
                              ? "bg-cyan-500/20 border border-cyan-500/30 text-cyan-400"
                              : "text-gray-400 hover:bg-slate-800/60 hover:text-gray-200"
                          }`}
                        >
                          <div className="flex items-center space-x-2">
                            <span className="text-base">{channel.icon}</span>
                            <div>
                              <span className="text-sm font-medium">
                                {channel.name.replace("# ", "")}
                              </span>
                              <div className="text-xs text-gray-500">
                                {channel.members.toLocaleString()} members
                              </div>
                            </div>
                          </div>
                          {channel.only_admin && (
                            <Crown className="w-3 h-3 text-orange-400" />
                          )}
                        </div>
                      )
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col bg-slate-900/95">
          {/* Chat Header */}
          <div className="bg-slate-900/95 border-b border-slate-800 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="text-xl">
                  {channels.find((c) => c.id === activeChannel)?.icon}
                </div>
                <div>
                  <h1 className="text-lg font-bold text-white">
                    #
                    {channels
                      .find((c) => c.id === activeChannel)
                      ?.name.replace("# ", "") || activeChannel}
                  </h1>
                  <div className="text-gray-400 text-xs flex items-center gap-2">
                    <span>
                      {channels
                        .find((c) => c.id === activeChannel)
                        ?.members.toLocaleString()}{" "}
                      members
                    </span>
                    <span>•</span>
                    <span className="text-green-400">
                      {channels.find((c) => c.id === activeChannel)?.online}{" "}
                      online
                    </span>
                    {channels.find((c) => c.id === activeChannel)
                      ?.only_admin && (
                      <>
                        <span>•</span>
                        <span className="text-orange-400 flex items-center gap-1">
                          <Crown className="w-3 h-3" />
                          Admin Only
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Real-time connection status */}
              <div className="flex items-center space-x-2">
                {socketConnected ? (
                  <Wifi className="h-4 w-4 text-green-400" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-400" />
                )}
              </div>
            </div>
          </div>

          {/* Messages */}
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto p-4 space-y-4 bg-slate-900/50"
          >
            {messagesLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-gray-400 flex items-center gap-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-cyan-400"></div>
                  Loading messages...
                </div>
              </div>
            ) : messagesError ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-red-400 text-center">
                  <p>Error loading messages</p>
                  <button
                    onClick={() => refetchMessages()}
                    className="mt-2 text-sm text-cyan-400 hover:text-cyan-300"
                  >
                    Try again
                  </button>
                </div>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-gray-400 text-center">
                  <MessageCircle className="h-10 w-10 mx-auto mb-3 opacity-50" />
                  <p>No messages yet in this channel</p>
                  <p className="text-sm">
                    Be the first to start the conversation!
                  </p>
                </div>
              </div>
            ) : (
              messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`group relative ${
                    msg.pinned
                      ? "bg-orange-500/10 border border-orange-500/30 rounded-xl p-3"
                      : ""
                  }`}
                >
                  {msg.pinned && (
                    <div className="flex items-center space-x-2 mb-2 text-orange-400 text-xs font-medium">
                      <Pin className="h-3 w-3" />
                      <Crown className="h-3 w-3" />
                      <span>Pinned Announcement</span>
                    </div>
                  )}

                  <div className="flex items-start space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 rounded-lg bg-slate-800 flex items-center justify-center text-lg border border-slate-700">
                        {/* {msg.avatar} */}
                      </div>
                      {msg.staff && (
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
                          <Crown className="w-2 h-2 text-slate-900" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <span
                          className={`font-bold text-base ${
                            msg.staff ? "text-orange-400" : "text-gray-200"
                          }`}
                        >
                          {msg.user}
                        </span>

                        {msg.staff && (
                          <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-slate-900 text-xs px-2 py-0.5 font-bold rounded-full flex items-center">
                            <Crown className="w-2 h-2 mr-1" />
                            STAFF
                          </div>
                        )}

                        <span className="text-xs text-gray-500">
                          {msg.timestamp}
                        </span>
                      </div>

                      <p className="text-gray-300 mb-2 leading-relaxed break-words text-sm">
                        {msg.message}
                      </p>

                      <div className="flex flex-wrap gap-2">
                        {/* Existing Reactions */}
                        {msg.reactions.map((reaction, index) => (
                          <button
                            key={index}
                            onClick={() =>
                              handleReaction(msg.message_id, reaction.emoji)
                            }
                            disabled={addReactionMutation.isPending}
                            className="flex items-center space-x-1 bg-slate-800/60 border border-slate-700 rounded-full px-2 py-1 text-xs hover:bg-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300 disabled:opacity-50"
                          >
                            <span className="text-sm">{reaction.emoji}</span>
                            <span className="font-medium text-gray-300">
                              {reaction.count}
                            </span>
                          </button>
                        ))}

                        {/* Add Reaction Button */}
                        {userData && (
                          <button
                            className="flex items-center justify-center w-7 h-7 bg-slate-800/60 border border-slate-700 rounded-full text-xs hover:bg-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300"
                            disabled={addReactionMutation.isPending}
                            onClick={() => handleReaction(msg.message_id, "👍")}
                          >
                            {addReactionMutation.isPending ? (
                              <div className="animate-spin rounded-full h-2 w-2 border-b border-gray-400"></div>
                            ) : (
                              <Smile className="h-3 w-3 text-gray-400" />
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}

            {/* Loading indicators */}
            {sendMessageMutation.isPending && (
              <div className="text-center text-gray-400">
                <div className="inline-flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
                  Sending message...
                </div>
              </div>
            )}

            {/* Scroll to bottom button */}
            {!isAtBottom() && (
              <div className="fixed bottom-24 right-4 z-10">
                <button
                  onClick={() => scrollToBottom(true)}
                  className="bg-cyan-500 hover:bg-cyan-600 text-white p-2 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
                  title="Scroll to bottom"
                >
                  <MessageCircle className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>

          {/* Message Input */}
          <div className="p-4 border-t border-slate-800 bg-slate-900/95">
            {(() => {
              const currentChannel = channels.find(
                (c) => c.id === activeChannel
              );

              if (!userData) {
                return (
                  <div className="text-center p-6 bg-slate-900/90 border border-slate-800 rounded-xl">
                    <div className="w-12 h-12 bg-cyan-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <MessageCircle className="h-6 w-6 text-cyan-400" />
                    </div>
                    <h3 className="text-lg font-bold mb-2 text-white">
                      Join the Conversation
                    </h3>
                    <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                      Connect your wallet to participate in community
                      discussions.
                    </p>
                    <button
                      onClick={() => setShowAccountConnectModal(true)}
                      className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300"
                    >
                      Connect Wallet
                    </button>
                  </div>
                );
              }

              if (currentChannel?.only_admin && !userData.is_admin) {
                return (
                  <div className="text-center p-4 bg-orange-500/10 border border-orange-500/30 rounded-xl">
                    <div className="w-10 h-10 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Crown className="h-5 w-5 text-orange-400" />
                    </div>
                    <h3 className="text-base font-bold mb-1 text-white">
                      Admin Only Channel
                    </h3>
                    <p className="text-gray-400 text-xs leading-relaxed">
                      Only administrators can send messages in this channel.
                    </p>
                  </div>
                );
              }

              if (
                currentChannel?.only_moderators &&
                !userData.is_moderator &&
                !userData.is_admin
              ) {
                return (
                  <div className="text-center p-4 bg-purple-500/10 border border-purple-500/30 rounded-xl">
                    <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Shield className="h-5 w-5 text-purple-400" />
                    </div>
                    <h3 className="text-base font-bold mb-1 text-white">
                      Moderator Only Channel
                    </h3>
                    <p className="text-gray-400 text-xs leading-relaxed">
                      Only moderators and administrators can send messages in
                      this channel.
                    </p>
                  </div>
                );
              }

              return (
                <div className="space-y-3">
                  {/* User info banner */}
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 rounded-full bg-slate-800 flex items-center justify-center border border-slate-700">
                        <span className="text-xs font-medium">
                          {userData.in_game_name?.[0] || userData.first_name[0]}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-300 font-medium text-sm">
                          {userData.in_game_name ||
                            `${userData.first_name} ${userData.last_name}`}
                        </span>
                        {userData.is_admin && (
                          <div className="bg-orange-500 text-slate-900 text-xs px-1 py-0.5 font-bold rounded-full flex items-center">
                            <Crown className="w-2 h-2 mr-1" />
                            ADMIN
                          </div>
                        )}
                        {userData.is_moderator && !userData.is_admin && (
                          <div className="bg-purple-500 text-white text-xs px-1 py-0.5 font-bold rounded-full flex items-center">
                            <Shield className="w-2 h-2 mr-1" />
                            MOD
                          </div>
                        )}
                        {userData.social_points && (
                          <span className="text-cyan-400 text-xs">
                            {userData.social_points.toLocaleString()} SP
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Message input */}
                  <div className="flex items-end space-x-3">
                    <div className="flex-1">
                      <textarea
                        placeholder={`Share your epic gaming moments in #${
                          currentChannel?.name?.replace("# ", "") ||
                          activeChannel
                        }...`}
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                        disabled={sendMessageMutation.isPending}
                        className="w-full resize-none bg-slate-800/60 border border-slate-700 focus:border-cyan-500 max-h-24 rounded-lg text-gray-200 placeholder:text-gray-500 p-3 outline-none transition-colors disabled:opacity-50 text-sm"
                        rows={1}
                      />
                    </div>
                    <button
                      onClick={handleSendMessage}
                      disabled={
                        !message.trim() || sendMessageMutation.isPending
                      }
                      className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-bold p-3 rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[44px]"
                    >
                      {sendMessageMutation.isPending ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </button>
                  </div>

                  {/* Channel info */}
                  {currentChannel && (
                    <div className="text-xs text-gray-500 flex items-center space-x-3">
                      {currentChannel.only_admin && (
                        <div className="flex items-center space-x-1">
                          <Crown className="w-3 h-3 text-orange-400" />
                          <span>Admin permissions granted</span>
                        </div>
                      )}
                      {currentChannel.only_moderators &&
                        !currentChannel.only_admin && (
                          <div className="flex items-center space-x-1">
                            <Shield className="w-3 h-3 text-purple-400" />
                            <span>Moderator permissions granted</span>
                          </div>
                        )}
                      <span>Press Enter to send</span>
                    </div>
                  )}
                </div>
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileCommunity;
