import React, { useState, useEffect } from "react";
import { X, User, AlertCircle, CheckCircle, Smartphone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGame } from "@/contexts/GameContext";
import { useConnectAccount } from "@/hooks/use-game-api";
import { toast } from "@/hooks/use-toast";

interface MobileAccountConnectModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MobileAccountConnectModal: React.FC<MobileAccountConnectModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [accountToken, setAccountToken] = useState("");
  const [tgId, setTgId] = useState("");
  const [error, setError] = useState("");
  const {
    setAuthToken,
    setCallConnectWithAuthApi,
    setUserData,
    setGameStats,
    setLastPlayedGames,
  } = useGame();
  const connectAccountMutation = useConnectAccount();

  const isValidating = connectAccountMutation.isPending;

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setError("");
      setTgId(""); // Clear tg_id when modal closes
    }
  }, [isOpen]);

  // Helper function to check if tg_id is valid
  const isTgIdValid = (tgId: string): boolean => {
    // Telegram ID should be a numeric string
    const tgIdPattern = /^\d+$/;
    return tgId.length > 0 && tgIdPattern.test(tgId);
  };

  // Function to get validation error message
  const getValidationError = (token: string, tgId: string): string => {
    const trimmedToken = token.trim();
    const trimmedTgId = tgId.trim();

    if (!trimmedToken) {
      return "Account Token is required";
    }

    if (!trimmedTgId) {
      return "Telegram ID is required";
    }

    // Telegram ID validation - should be numeric
    const tgIdPattern = /^\d+$/;
    if (!tgIdPattern.test(trimmedTgId)) {
      return "Telegram ID must be a valid numeric ID";
    }

    return "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = getValidationError(accountToken, tgId);
    if (validationError) {
      setError(validationError);
      toast({
        title: "Validation Error",
        description: validationError,
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await connectAccountMutation.mutateAsync({
        token: accountToken.trim(),
        tg_id: tgId.trim(),
      });

      //
      setCallConnectWithAuthApi(false);

      // Store the auth token and game stats
      setAuthToken(result.auth_token);
      setUserData(result.user_data);
      setGameStats(result.game_stats);
      setLastPlayedGames(result.last_4_played_games);

      toast({
        title: "Account Connected Successfully!",
        description: `Welcome back, ${result.user_data.first_name}! Your gaming journey continues.`,
      });

      // Close modal after successful connection
      onClose();
    } catch (error: any) {
      console.error("Account connection error:", error);
      setError(
        error?.response?.data?.message ||
          error?.message ||
          "Failed to connect account. Please try again."
      );
      toast({
        title: "Connection Failed",
        description:
          error?.response?.data?.message ||
          error?.message ||
          "Failed to connect account. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm">
      <div className="relative w-full max-w-md bg-gradient-to-br from-card-dark to-surface-dark border border-border-light/30 rounded-2xl shadow-2xl overflow-hidden">
        {/* Mobile Header */}
        <div className="relative p-6 pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-xl flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Connect Account</h2>
                <p className="text-sm text-secondary-text">Join the gaming multiverse</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-surface-dark/50"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Mobile Info Banner */}
          <div className="bg-accent-cyan/10 border border-accent-cyan/20 rounded-xl p-4 mb-4">
            <div className="flex items-start space-x-3">
              <Smartphone className="w-5 h-5 text-accent-cyan mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-accent-cyan font-medium mb-1">Mobile Optimized</p>
                <p className="text-secondary-text">
                  Connect your account securely from any device to access your gaming profile and rewards.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Form */}
        <form onSubmit={handleSubmit} className="p-6 pt-0 space-y-4">
          {/* Account Token Input */}
          <div className="space-y-2">
            <Label htmlFor="accountToken" className="text-sm font-medium text-white">
              Account Token
            </Label>
            <div className="relative">
              <Input
                id="accountToken"
                type="text"
                placeholder="Enter your account token"
                value={accountToken}
                onChange={(e) => setAccountToken(e.target.value)}
                className="w-full h-12 bg-surface-dark/60 border-border-light/50 text-primary-text placeholder:text-muted-text focus:border-accent-cyan focus:ring-accent-cyan rounded-xl pl-4"
                disabled={isValidating}
              />
            </div>
            <p className="text-xs text-muted-text">
              This is your unique authentication token for secure access
            </p>
          </div>

          {/* Telegram ID Input */}
          <div className="space-y-2">
            <Label htmlFor="tgId" className="text-sm font-medium text-white">
              Telegram ID
            </Label>
            <div className="relative">
              <Input
                id="tgId"
                type="text"
                placeholder="Enter your Telegram ID"
                value={tgId}
                onChange={(e) => setTgId(e.target.value)}
                className="w-full h-12 bg-surface-dark/60 border-border-light/50 text-primary-text placeholder:text-muted-text focus:border-accent-cyan focus:ring-accent-cyan rounded-xl pl-4"
                disabled={isValidating}
              />
            </div>
            <p className="text-xs text-muted-text">
              Your numeric Telegram ID for account verification
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="text-red-400 font-medium mb-1">Connection Error</p>
                  <p className="text-red-300">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Success State */}
          {connectAccountMutation.isSuccess && (
            <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="text-green-400 font-medium mb-1">Success!</p>
                  <p className="text-green-300">
                    Your account has been connected successfully. Welcome to JargonQuest!
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Mobile Action Buttons */}
          <div className="space-y-3 pt-2">
            <Button
              type="submit"
              disabled={isValidating || !accountToken.trim() || !tgId.trim()}
              className="w-full h-14 bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold text-base rounded-xl hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:scale-100"
            >
              {isValidating ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Connecting...</span>
                </div>
              ) : (
                <span>Connect Account</span>
              )}
            </Button>

            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              className="w-full h-12 bg-transparent border-2 border-border-light/50 text-secondary-text hover:bg-surface-dark/50 hover:text-white transition-all duration-300 rounded-xl"
            >
              Cancel
            </Button>
          </div>

          {/* Mobile Help Text */}
          <div className="text-center pt-2">
            <p className="text-xs text-muted-text">
              Need help? Contact our support team for assistance
            </p>
          </div>
        </form>

        {/* Mobile Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/5 via-transparent to-xp-purple/5 pointer-events-none" />
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-accent-cyan to-xp-purple" />
      </div>
    </div>
  );
};
