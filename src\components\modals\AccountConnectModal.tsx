import React, { useState, useEffect } from "react";
import { X, User, AlertCircle, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGame } from "@/contexts/GameContext";
import { useConnectAccount } from "@/hooks/use-game-api";
import { toast } from "@/hooks/use-toast";

interface AccountConnectModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AccountConnectModal: React.FC<AccountConnectModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [accountToken, setAccountToken] = useState("");
  const [tgId, setTgId] = useState("");
  const [error, setError] = useState("");
  const {
    setAuthToken,
    setCallConnectWithAuthApi,
    setUserData,
    setGameStats,
    setLastPlayedGames,
  } = useGame();
  const connectAccountMutation = useConnectAccount();

  const isValidating = connectAccountMutation.isPending;

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setError("");
      setTgId(""); // Clear tg_id when modal closes
    }
  }, [isOpen]);

  // Helper function to check if tg_id is valid
  const isTgIdValid = (tgId: string): boolean => {
    // Telegram ID should be a numeric string
    const tgIdPattern = /^\d+$/;
    return tgId.length > 0 && tgIdPattern.test(tgId);
  };

  // Function to get validation error message
  const getValidationError = (token: string, tgId: string): string => {
    const trimmedToken = token.trim();
    const trimmedTgId = tgId.trim();

    if (!trimmedToken) {
      return "Account Token is required";
    }

    if (!trimmedTgId) {
      return "Telegram ID is required";
    }

    // Telegram ID validation - should be numeric
    const tgIdPattern = /^\d+$/;
    if (!tgIdPattern.test(trimmedTgId)) {
      return "Telegram ID must be a valid numeric ID";
    }

    return "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = getValidationError(accountToken, tgId);
    if (validationError) {
      setError(validationError);
      toast({
        title: "Validation Error",
        description: validationError,
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await connectAccountMutation.mutateAsync({
        token: accountToken.trim(),
        tg_id: tgId.trim(),
      });

      //
      setCallConnectWithAuthApi(false);

      // Store the auth token and game stats
      setAuthToken(result.auth_token);
      setUserData(result.user_data);
      setGameStats(result.game_stats);
      setLastPlayedGames(result.last_4_played_games);

      toast({
        title: "Account Connected Successfully!",
        description: "Your progress will now be tracked across all games.",
      });

      onClose();
    } catch (error: any) {
      const errorMessage =
        error?.message || "Failed to connect account. Please try again.";
      setError(errorMessage);

      toast({
        title: "Connection Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAccountToken(value);

    // Clear error when user starts typing
    if (error) {
      setError("");
    }
  };

  const handleTgIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTgId(value);

    // Clear error when user starts typing
    if (error) {
      setError("");
    }

    // Real-time validation for tg_id - only allow numbers
    if (value && !/^\d*$/.test(value)) {
      setError("Telegram ID must contain only numbers");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-card-dark border border-border-light rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-accent-cyan/10 rounded-lg">
              <User className="h-5 w-5 text-accent-cyan" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-primary-text">
                Connect Account
              </h2>
              <p className="text-sm text-secondary-text">
                Required to track your game progress
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-secondary-text hover:text-primary-text"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="account-token" className="text-primary-text">
              Account Token
            </Label>
            <Input
              id="account-token"
              type="text"
              value={accountToken}
              onChange={handleInputChange}
              placeholder="Enter your account token"
              className={`bg-surface-dark text-primary-text placeholder:text-muted-text ${
                accountToken.trim().length > 0
                  ? "border-green-500 focus:border-green-500"
                  : "border-border-light"
              }`}
              disabled={isValidating}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tg_id" className="text-primary-text">
              Telegram ID
            </Label>
            <Input
              id="tg_id"
              type="text"
              value={tgId}
              onChange={handleTgIdChange}
              placeholder="Enter your Telegram ID"
              className={`bg-surface-dark text-primary-text placeholder:text-muted-text ${
                isTgIdValid(tgId)
                  ? "border-green-500 focus:border-green-500"
                  : tgId.length > 0 && !isTgIdValid(tgId)
                  ? "border-red-500 focus:border-red-500"
                  : "border-border-light"
              }`}
              disabled={isValidating}
            />
            <div className="space-y-1">
              <p className="text-xs text-muted-text">
                Your numeric Telegram user ID (e.g., 123456789)
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isValidating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-accent-cyan to-xp-purple"
              disabled={
                isValidating || !accountToken.trim() || !tgId.trim()
              }
            >
              {isValidating ? "Validating..." : "Continue"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
