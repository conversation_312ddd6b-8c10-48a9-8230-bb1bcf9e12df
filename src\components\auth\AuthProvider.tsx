import React, { useEffect, useRef } from "react";
import { useGame } from "@/contexts/GameContext";
import { useConnectAccountWithAuth } from "@/hooks/use-game-api";
import { toast } from "@/hooks/use-toast";

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const {
    authToken,
    callConnectWithAuthApi,
    setUserData,
    setGameStats,
    setLastPlayedGames,
    clearAuthToken,
    setIsAuthLoading,
  } = useGame();
  const hasShownWelcomeToast = useRef(false);

  // Automatically connect with auth token if available
  const { data: authData, error: authError, isLoading: authQueryLoading } = useConnectAccountWithAuth(
    authToken || "",
    callConnectWithAuthApi
  );

  // Update loading state in context
  useEffect(() => {
    setIsAuthLoading(authQueryLoading);
  }, [authQueryLoading, setIsAuthLoading]);

  useEffect(() => {
    if (authData) {
      // Update auth token and game stats from server response
      setUserData(authData.user_data);
      setGameStats(authData.game_stats);
      setLastPlayedGames(authData.last_4_played_games);

      // Only show welcome toast once per session
      if (!hasShownWelcomeToast.current) {
        toast({
          title: "Welcome Back!",
          description: "Your account has been automatically connected.",
        });
        hasShownWelcomeToast.current = true;
      }
    }
  }, [authData, setUserData, setGameStats]);

  useEffect(() => {
    if (authError) {
      // If auth token is invalid, clear it
      console.error("Authentication failed:", authError);
      clearAuthToken();

      toast({
        title: "Authentication Expired",
        description: "Please connect your account again to track progress.",
        variant: "destructive",
      });
    }
  }, [authError, clearAuthToken]);

  return <>{children}</>;
};
