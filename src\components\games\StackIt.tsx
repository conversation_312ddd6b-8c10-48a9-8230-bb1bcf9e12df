import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import * as CANN<PERSON> from 'cannon';

interface StackItProps {
  onScoreUpdate?: (score: number) => void;
  onGameEnd?: (finalScore: number) => void;
}

const StackIt: React.FC<StackItProps> = ({ onScoreUpdate, onGameEnd }) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const gameRef = useRef({
    camera: null as THREE.OrthographicCamera | null,
    scene: null as THREE.Scene | null,
    renderer: null as THREE.WebGLRenderer | null,
    world: null as CANNON.World | null,
    lastTime: 0,
    stack: [] as any[],
    overhangs: [] as any[],
    autopilot: true,
    gameEnded: false,
    robotPrecision: 0
  });
  
  const [score, setScore] = useState(0);
  const [showInstructions, setShowInstructions] = useState(true);
  const [showResults, setShowResults] = useState(false);
  
  const boxHeight = 1;
  const originalBoxSize = 3;

  const getContainerDimensions = () => {
    if (mountRef.current) {
      return {
        width: mountRef.current.clientWidth,
        height: mountRef.current.clientHeight
      };
    }
    return { width: 800, height: 600 }; // fallback dimensions
  };

  const setRobotPrecision = () => {
    gameRef.current.robotPrecision = Math.random() * 1 - 0.5;
  };

  const generateBox = (x: number, y: number, z: number, width: number, depth: number, falls: boolean) => {
    const { scene, world, stack } = gameRef.current;
    if (!scene || !world) return null;
    
    // THREE.js mesh
    const geometry = new THREE.BoxGeometry(width, boxHeight, depth);
    const color = new THREE.Color(`hsl(${30 + stack.length * 4}, 100%, 50%)`);
    const material = new THREE.MeshLambertMaterial({ color });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(x, y, z);
    scene.add(mesh);

    // Cannon.js physics body
    const shape = new CANNON.Box(new CANNON.Vec3(width / 2, boxHeight / 2, depth / 2));
    let mass = falls ? 5 : 0;
    mass *= width / originalBoxSize;
    mass *= depth / originalBoxSize;
    const body = new CANNON.Body({ mass, shape });
    body.position.set(x, y, z);
    world.addBody(body);

    return {
      threejs: mesh,
      cannonjs: body,
      width,
      depth
    };
  };

  const addLayer = (x: number, z: number, width: number, depth: number, direction?: string) => {
    const { stack } = gameRef.current;
    const y = boxHeight * stack.length;
    const layer = generateBox(x, y, z, width, depth, false);
    if (layer) {
      (layer as any).direction = direction;
      stack.push(layer);
    }
  };

  const addOverhang = (x: number, z: number, width: number, depth: number) => {
    const { stack, overhangs } = gameRef.current;
    const y = boxHeight * (stack.length - 1);
    const overhang = generateBox(x, y, z, width, depth, true);
    if (overhang) {
      overhangs.push(overhang);
    }
  };

  const cutBox = (topLayer: any, overlap: number, size: number, delta: number) => {
    const direction = topLayer.direction;
    const newWidth = direction === "x" ? overlap : topLayer.width;
    const newDepth = direction === "z" ? overlap : topLayer.depth;

    // Update metadata
    topLayer.width = newWidth;
    topLayer.depth = newDepth;

    // Update THREE.js model
    topLayer.threejs.scale[direction] = overlap / size;
    topLayer.threejs.position[direction] -= delta / 2;

    // Update Cannon.js model
    topLayer.cannonjs.position[direction] -= delta / 2;
    const shape = new CANNON.Box(new CANNON.Vec3(newWidth / 2, boxHeight / 2, newDepth / 2));
    topLayer.cannonjs.shapes = [];
    topLayer.cannonjs.addShape(shape);
  };

  const splitBlockAndAddNextOneIfOverlaps = () => {
    const { stack, gameEnded } = gameRef.current;
    if (gameEnded) return;

    const topLayer = stack[stack.length - 1];
    const previousLayer = stack[stack.length - 2];
    const direction = topLayer.direction;
    const size = direction === "x" ? topLayer.width : topLayer.depth;
    const delta = topLayer.threejs.position[direction] - previousLayer.threejs.position[direction];
    const overhangSize = Math.abs(delta);
    const overlap = size - overhangSize;

    if (overlap > 0) {
      cutBox(topLayer, overlap, size, delta);

      // Create overhang
      const overhangShift = (overlap / 2 + overhangSize / 2) * Math.sign(delta);
      const overhangX = direction === "x" 
        ? topLayer.threejs.position.x + overhangShift 
        : topLayer.threejs.position.x;
      const overhangZ = direction === "z" 
        ? topLayer.threejs.position.z + overhangShift 
        : topLayer.threejs.position.z;
      const overhangWidth = direction === "x" ? overhangSize : topLayer.width;
      const overhangDepth = direction === "z" ? overhangSize : topLayer.depth;
      addOverhang(overhangX, overhangZ, overhangWidth, overhangDepth);

      // Add next layer
      const nextX = direction === "x" ? topLayer.threejs.position.x : -10;
      const nextZ = direction === "z" ? topLayer.threejs.position.z : -10;
      const nextDirection = direction === "x" ? "z" : "x";
      
      const newScore = stack.length - 1;
      setScore(newScore);

      // Only update score via API when game is actually being played (not during autopilot demo)
      if (!gameRef.current.autopilot) {
        onScoreUpdate?.(newScore);
      }

      addLayer(nextX, nextZ, topLayer.width, topLayer.depth, nextDirection);
    } else {
      missedTheSpot();
    }
  };

  const missedTheSpot = () => {
    const { stack, world, scene } = gameRef.current;
    if (!world || !scene) return;
    
    const topLayer = stack[stack.length - 1];

    addOverhang(
      topLayer.threejs.position.x,
      topLayer.threejs.position.z,
      topLayer.width,
      topLayer.depth
    );

    world.remove(topLayer.cannonjs);
    scene.remove(topLayer.threejs);
    gameRef.current.gameEnded = true;
    
    if (!gameRef.current.autopilot) {
      setShowResults(true);
      onGameEnd?.(score);
    }
  };

  const updatePhysics = (timePassed: number) => {
    const { world, overhangs } = gameRef.current;
    if (!world) return;
    
    world.step(timePassed / 1000);
    
    overhangs.forEach(element => {
      element.threejs.position.copy(element.cannonjs.position);
      element.threejs.quaternion.copy(element.cannonjs.quaternion);
    });
  };

  const animation = (time: number) => {
    const game = gameRef.current;
    if (!game.camera || !game.renderer || !game.scene) return;
    
    if (game.lastTime) {
      const timePassed = time - game.lastTime;
      const speed = 0.008;
      const topLayer = game.stack[game.stack.length - 1];
      const previousLayer = game.stack[game.stack.length - 2];

      const boxShouldMove = !game.gameEnded && 
        (!game.autopilot || 
         (game.autopilot && topLayer.threejs.position[topLayer.direction] < 
          previousLayer.threejs.position[topLayer.direction] + game.robotPrecision));

      if (boxShouldMove) {
        topLayer.threejs.position[topLayer.direction] += speed * timePassed;
        topLayer.cannonjs.position[topLayer.direction] += speed * timePassed;

        if (topLayer.threejs.position[topLayer.direction] > 10) {
          missedTheSpot();
        }
      } else if (game.autopilot) {
        splitBlockAndAddNextOneIfOverlaps();
        setRobotPrecision();
      }

      // Camera follow
      if (game.camera.position.y < boxHeight * (game.stack.length - 2) + 4) {
        game.camera.position.y += speed * timePassed;
      }

      updatePhysics(timePassed);
      game.renderer.render(game.scene, game.camera);
    }
    game.lastTime = time;
  };

  const startGame = () => {
    const game = gameRef.current;
    if (!game.world || !game.scene || !game.camera) return;
    
    game.autopilot = false;
    game.gameEnded = false;
    game.lastTime = 0;
    game.stack = [];
    game.overhangs = [];
    
    setScore(0);
    setShowInstructions(false);
    setShowResults(false);

    // Notify parent component that game has started with initial score
    onScoreUpdate?.(0);

    // Clean up existing objects
    while (game.world.bodies.length > 0) {
      game.world.remove(game.world.bodies[0]);
    }
    while (game.scene.children.find(c => c.type === "Mesh")) {
      const mesh = game.scene.children.find(c => c.type === "Mesh");
      if (mesh) game.scene.remove(mesh);
    }

    // Reset camera
    game.camera.position.set(4, 4, 4);
    game.camera.lookAt(0, 0, 0);

    // Add foundation and first layer
    addLayer(0, 0, originalBoxSize, originalBoxSize);
    addLayer(-10, 0, originalBoxSize, originalBoxSize, "x");
  };

  const handleInput = () => {
    if (gameRef.current.autopilot) {
      startGame();
    } else {
      splitBlockAndAddNextOneIfOverlaps();
    }
  };

  useEffect(() => {
    const game = gameRef.current;
    
    // Initialize Cannon.js
    game.world = new CANNON.World();
    game.world.gravity.set(0, -10, 0);
    game.world.broadphase = new CANNON.NaiveBroadphase();
    game.world.solver.iterations = 40;

    // Initialize THREE.js with container dimensions
    const { width, height } = getContainerDimensions();
    const aspect = width / height;
    const viewWidth = 10;
    const viewHeight = viewWidth / aspect;
    
    game.camera = new THREE.OrthographicCamera(
      viewWidth / -2, viewWidth / 2, viewHeight / 2, viewHeight / -2, 0, 100
    );
    game.camera.position.set(4, 4, 4);
    game.camera.lookAt(0, 0, 0);

    game.scene = new THREE.Scene();

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    game.scene.add(ambientLight);
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.6);
    dirLight.position.set(10, 20, 0);
    game.scene.add(dirLight);

    // Renderer
    game.renderer = new THREE.WebGLRenderer({ antialias: true });
    game.renderer.setSize(width, height);
    game.renderer.setAnimationLoop(animation);
    
    if (mountRef.current) {
      mountRef.current.appendChild(game.renderer.domElement);
    }

    // Initialize game
    setRobotPrecision();
    addLayer(0, 0, originalBoxSize, originalBoxSize);
    addLayer(-10, 0, originalBoxSize, originalBoxSize, "x");

    // Event listeners
    const handleClick = () => handleInput();
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === " ") {
        event.preventDefault();
        handleInput();
      } else if (event.key === "R" || event.key === "r") {
        event.preventDefault();
        startGame();
      }
    };

    const handleResize = () => {
      if (!game.camera || !game.renderer) return;
      
      const { width, height } = getContainerDimensions();
      const aspect = width / height;
      const viewWidth = 10;
      const viewHeight = viewWidth / aspect;
      
      game.camera.left = viewWidth / -2;
      game.camera.right = viewWidth / 2;
      game.camera.top = viewHeight / 2;
      game.camera.bottom = viewHeight / -2;
      game.camera.updateProjectionMatrix();
      
      game.renderer.setSize(width, height);
    };

    // Use ResizeObserver to watch for container size changes
    const resizeObserver = new ResizeObserver(() => {
      handleResize();
    });

    if (mountRef.current) {
      resizeObserver.observe(mountRef.current);
    }

    window.addEventListener("mousedown", handleClick);
    window.addEventListener("touchstart", handleClick);
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("mousedown", handleClick);
      window.removeEventListener("touchstart", handleClick);
      window.removeEventListener("keydown", handleKeyDown);
      
      if (game.renderer && mountRef.current && game.renderer.domElement.parentNode) {
        mountRef.current.removeChild(game.renderer.domElement);
      }
    };
  }, []);

  return (
    <div className="stackit-game w-full h-full relative" style={{ minHeight: '400px' }}>
      <div ref={mountRef} className="w-full h-full" />
      
      {/* Score */}
      <div className="absolute top-4 right-4 text-white text-3xl font-bold font-sans z-10">
        {score}
      </div>

      {/* Instructions */}
      {showInstructions && (
        <div className="absolute inset-0 bg-black/75 flex items-center justify-center text-white font-sans z-20">
          <div className="max-w-sm p-8 rounded-2xl text-center">
            <h1 className="text-2xl font-bold text-accent-cyan mb-4">Stack It</h1>
            <p className="mb-3 text-sm">Stack the blocks on top of each other</p>
            <p className="mb-3 text-sm">Click, tap or press Space when a block is above the stack</p>
            <button
              onClick={handleInput}
              className="px-6 py-3 bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold rounded-lg hover:scale-105 transition-all duration-300"
            >
              Start Game
            </button>
          </div>
        </div>
      )}

      {/* Results */}
      {showResults && (
        <div className="absolute inset-0 bg-black/75 flex items-center justify-center text-white font-sans z-20">
          <div className="max-w-sm p-8 rounded-2xl text-center">
            <h2 className="text-xl font-bold text-red-400 mb-3">Game Over!</h2>
            <p className="mb-2 text-sm">Final Score: {score}</p>
            <p className="mb-4 text-sm">You missed the block</p>
            <button
              onClick={startGame}
              className="px-6 py-3 bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold rounded-lg hover:scale-105 transition-all duration-300"
            >
              Play Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default StackIt;