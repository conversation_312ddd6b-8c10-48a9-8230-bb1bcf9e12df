import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Play,
  Users,
  Coins,
  Filter,
  Search,
  Star,
  Trophy,
  Zap,
  Target,
  Crown,
  Globe,
  TrendingUp,
  Clock,
  Award,
  Loader2,
  AlertCircle,
  ChevronDown,
  X,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useWallet } from "@/contexts/WalletContext";
import { useGame } from "@/contexts/GameContext";
import { useGames } from "@/hooks/use-game-api";
import { Game } from "@/types/api";
import MobileGameCard from "@/components/ui/MobileGameCard";
import { gameUrls } from "@/game-data";

const MobileGames = () => {
  const { isConnected, connectWallet } = useWallet();
  const { openAccountConnectModal, userData, setSelectedGame, isAuthLoading } = useGame();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBlockchain, setSelectedBlockchain] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [sortBy, setSortBy] = useState("popular");
  const [showFilters, setShowFilters] = useState(false);

  // Fetch games from API
  const { data: apiGames, isLoading, error } = useGames();

  // Transform API games to match the expected format
  const games =
    apiGames?.map((game: Game, index: number) => ({
      id: game._id,
      gameId: game.game_id,
      numericId: index + 1, // For sorting purposes
      title: game.title,
      description: game.description,
      image: game.image,
      blockchain: game.blockchain,
      category: game.category,
      playCount: 10000,
      rewards: `${game.min_reward}-${game.max_reward} JQ`,
      difficulty: game.difficulty,
      featured: game.featured,
      trending: game.trending,
      gameUrl: gameUrls[game.game_id],
    })) || [];

  const blockchains = ["all", "BSC", "SOL"];
  const categories = ["all", "Endless Runner", "Puzzle"];

  const filteredGames = games.filter((game) => {
    const matchesSearch =
      game.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      game.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBlockchain =
      selectedBlockchain === "all" || game.blockchain === selectedBlockchain;
    const matchesCategory =
      selectedCategory === "all" || game.category === selectedCategory;

    return matchesSearch && matchesBlockchain && matchesCategory;
  });

  const sortedGames = [...filteredGames].sort((a, b) => {
    switch (sortBy) {
      case "popular":
        return b.playCount - a.playCount;
      case "newest":
        return (b.numericId || 0) - (a.numericId || 0);
      case "rewards":
        return (
          parseInt(b.rewards.split("-")[1]) - parseInt(a.rewards.split("-")[1])
        );
      default:
        return 0;
    }
  });

  // Handle game selection
  const handleGameSelect = (game: any) => {
    // Convert the local game format back to API format for context
    const apiGame: Game = {
      _id: game.id,
      game_id: game.gameId,
      title: game.title,
      description: game.description,
      image: game.image,
      blockchain: game.blockchain,
      category: game.category,
      min_reward: parseInt(game.rewards.split("-")[0]),
      max_reward: parseInt(game.rewards.split("-")[1].replace(" JQ", "")),
      difficulty: game.difficulty,
      featured: game.featured,
      trending: game.trending,
      game_url: game.gameUrl,
    };
    setSelectedGame(apiGame);
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-slate-950">
      {/* Enhanced Background - Mobile optimized */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
        <div className="absolute inset-0 digital-grid opacity-10"></div>

        {/* Floating gaming elements - Reduced for mobile performance */}
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="absolute opacity-5 pointer-events-none"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float-advanced ${
                6 + Math.random() * 4
              }s ease-in-out infinite`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          >
            <div
              className="w-20 h-20 rounded-full"
              style={{
                background: `radial-gradient(circle, ${
                  ["#06B6D4", "#8B5CF6", "#F59E0B"][i % 3]
                }20, transparent 70%)`,
              }}
            />
          </div>
        ))}
      </div>

      <div className="relative z-10 pt-24 pb-8 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Mobile Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl md:text-4xl font-black mb-4">
              <span className="bg-gradient-to-r from-accent-cyan via-xp-purple to-gaming-gold bg-clip-text text-transparent">
                Gaming Universe
              </span>
            </h1>
            <div className="flex items-center justify-center gap-2 text-lg font-bold text-accent-cyan mb-4">
              <Target className="w-5 h-5" />
              <span>Discover • Play • Earn</span>
              <Trophy className="w-5 h-5" />
            </div>
            <p className="text-base text-secondary-text max-w-3xl mx-auto leading-relaxed">
              Explore the most epic Web3 games across multiple blockchains. Each game offers unique challenges, 
              crypto rewards, and opportunities to build your gaming legacy.
            </p>
          </div>

          {/* Mobile Search Bar */}
          <div className="mb-6">
            <div className="relative group">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-text group-focus-within:text-accent-cyan transition-colors duration-200" />
              <Input
                placeholder="Search epic games..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 h-12 bg-surface-dark/80 border-border-light/50 text-primary-text placeholder:text-muted-text focus:border-accent-cyan focus:ring-accent-cyan rounded-xl"
              />
            </div>
          </div>

          {/* Mobile Filters Toggle */}
          <div className="mb-6">
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="outline"
              className="w-full bg-surface-dark/80 border-border-light/50 text-primary-text hover:bg-surface-dark/90 rounded-xl h-12 flex items-center justify-between"
            >
              <span className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filters & Sorting
              </span>
              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
            </Button>
          </div>

          {/* Mobile Filters Panel */}
          {showFilters && (
            <div className="mb-6 p-4 bg-surface-dark/60 rounded-xl border border-border-light/30 space-y-4">
              {/* Blockchain Filter */}
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">
                  Blockchain
                </label>
                <div className="flex flex-wrap gap-2">
                  {blockchains.map((blockchain) => (
                    <button
                      key={blockchain}
                      onClick={() => setSelectedBlockchain(blockchain)}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        selectedBlockchain === blockchain
                          ? "bg-accent-cyan text-white"
                          : "bg-card-dark/60 text-secondary-text hover:bg-card-dark/80 hover:text-white"
                      }`}
                    >
                      {blockchain === "all" ? "All Chains" : blockchain}
                    </button>
                  ))}
                </div>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">
                  Category
                </label>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        selectedCategory === category
                          ? "bg-accent-cyan text-white"
                          : "bg-card-dark/60 text-secondary-text hover:bg-card-dark/80 hover:text-white"
                      }`}
                    >
                      {category === "all" ? "All Genres" : category}
                    </button>
                  ))}
                </div>
              </div>

              {/* Sort Options */}
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">
                  Sort By
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    { value: "popular", label: "Most Popular" },
                    { value: "newest", label: "Newest" },
                    { value: "rewards", label: "Best Rewards" },
                  ].map((option) => (
                    <button
                      key={option.value}
                      onClick={() => setSortBy(option.value)}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        sortBy === option.value
                          ? "bg-accent-cyan text-white"
                          : "bg-card-dark/60 text-secondary-text hover:bg-card-dark/80 hover:text-white"
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Clear Filters */}
              <div className="pt-2">
                <Button
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedBlockchain("all");
                    setSelectedCategory("all");
                    setSortBy("popular");
                  }}
                  variant="outline"
                  className="w-full bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30 rounded-lg"
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <Loader2 className="h-12 w-12 animate-spin text-accent-cyan mx-auto mb-4" />
                <p className="text-lg text-secondary-text">
                  Loading epic games...
                </p>
                <p className="text-sm text-muted-text mt-2">
                  Preparing your gaming universe
                </p>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="flex items-center justify-center py-16">
              <div className="text-center max-w-md">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-primary-text mb-2">
                  Unable to Load Games
                </h3>
                <p className="text-secondary-text mb-4">
                  We're having trouble connecting to our game servers. Please
                  check your connection and try again.
                </p>
                <Button
                  onClick={() => window.location.reload()}
                  className="bg-gradient-to-r from-accent-cyan to-xp-purple"
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {/* Enhanced All Games Section - Mobile optimized */}
          {!isLoading && !error && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl md:text-3xl font-black">
                  <span className="bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
                    All Games
                  </span>
                  <span className="text-secondary-text text-base ml-2">
                    ({sortedGames.length})
                  </span>
                </h2>
                <div className="flex items-center gap-2 text-sm text-secondary-text">
                  <Award className="w-4 h-4 text-accent-cyan" />
                  <span className="hidden sm:inline">Endless Adventures</span>
                </div>
              </div>

              {sortedGames.length === 0 ? (
                <div className="bg-gradient-to-br from-card-dark/80 to-surface-dark/60 backdrop-blur-xl border border-border-light/30 rounded-2xl text-center py-16">
                  <div className="text-6xl mb-4">🎮</div>
                  <p className="text-secondary-text text-xl mb-2">
                    No games found
                  </p>
                  <p className="text-muted-text">
                    Try adjusting your search criteria
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  {sortedGames.map((game, index) => (
                    <MobileGameCard
                      key={game.id}
                      game={{
                        id: game.id,
                        gameId: game.gameId,
                        title: game.title,
                        description: game.description,
                        image: game.image,
                        blockchain: game.blockchain,
                        rewards: game.rewards,
                        difficulty: game.difficulty,
                        status: game.featured ? "Featured" : "Live",
                        gradient: "from-card-dark/80 to-surface-dark/60",
                        borderGlow: "accent-cyan",
                        gameUrl: game.gameUrl,
                      }}
                      onGameSelect={handleGameSelect}
                      index={index}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MobileGames;
