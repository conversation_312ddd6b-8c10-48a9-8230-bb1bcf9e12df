<!DOCTYPE html>
<html>
<head>
<style>
body {
    height: 97vh;
    padding: 0;
    display: grid;
    align-content: center;
    justify-content: center;
}
</style>
</head>
<body>
<div id='fifteen'></div> <!--element "fifteen" in which the game will take place-->
<script>
var setup={
     puzzle_fifteen:{
        diff:300, // number of movements of the slots for shuffling pictures
        size:[512,640], // element size "fifteen" in pixels only
        grid:[3,4], // the number of squares in the height and width of the picture
        fill:true, // Stretching the area with the game to fit the element is recommended for fullscreen
        number:false, // Slot sequence number
        gamePad:true,
        art:{
            url:"art.jpg", // path to the picture (you can use any format of supported browsers, gif-animation of svg)
            ratio:false // enlarge the picture in height or width
        },
        // optional elements
        time:0.1, // block move animation time
        style:"border-radius:12px;", // style for puzzle square
        keyBoard:true // Control using the keys on the keyboard
     }
}
</script>
<script src="fifteen_puzzle.js"></script> <!--path to file engine-->
</body>
</html>
