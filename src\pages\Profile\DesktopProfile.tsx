import { useState } from "react";
import {
  Edit,
  Trophy,
  Star,
  Gamepad2,
  Users,
  TrendingUp,
  Calendar,
  Award,
  Sparkles,
  Zap,
  Shield,
  Target,
  Flame,
  Crown,
  Medal,
  Coins,
  Play,
  BarChart3,
  Clock,
  ChevronRight,
  Gift,
  Wallet,
  Copy,
  ExternalLink,
  Settings,
  Loader2,
} from "lucide-react";
import { useGame } from "@/contexts/GameContext";

const DesktopProfile = () => {
  const { userData, gameStats, lastPlayedGames, isAuthLoading } = useGame();

  const [isConnected] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const address = "0x742d35Cc6634C0532925a3b844Bc9e7595f65C";

  const gamesPlayed = Object.keys(gameStats || {})
    .map((gameId) => gameStats[gameId])
    .reduce((acc, stats) => acc + stats.games_played, 0);

  const profileData = {
    username: "CryptoGamer123",
    joinDate: "March 2024",
    socialPoints: 3420,
    jqPoints: 8950,
    gamesPlayed: 127,
    totalWins: 99,
  };

  const gameHistory = [
    {
      game: "Crypto Runner",
      score: 15420,
      reward: 125,
      result: "completed",
    },
    {
      game: "NFT Puzzle",
      score: 8750,
      reward: 87,
      result: "completed",
    },
    {
      game: "DeFi Defense",
      score: 12300,
      reward: 156,
      result: "completed",
    },
    {
      game: "Blockchain Shooter",
      score: 6890,
      reward: 69,
      result: "completed",
    },
  ];

  const AnimatedCounter = ({ value, decimals = 0, prefix = "" }) => {
    return (
      <span>
        {prefix}
        {value.toLocaleString(undefined, {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals,
        })}
      </span>
    );
  };

  if (isAuthLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-slate-950">
        <div className="relative bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-xl p-8 max-w-sm mx-auto shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/5 to-xp-purple/5 rounded-xl pointer-events-none" />
          <div className="relative text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-accent-cyan/25">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
            <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Loading Profile
            </h2>
            <p className="text-secondary-text text-sm leading-relaxed">
              Please wait while we fetch your profile data.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-slate-950">
        <div className="relative bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-xl p-8 max-w-sm mx-auto shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/5 to-xp-purple/5 rounded-xl pointer-events-none" />
          <div className="relative text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-accent-cyan/25">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Account
            </h2>
            <p className="text-secondary-text text-sm leading-relaxed">
              Please connect your account to view your gaming profile and track
              your achievements.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mt-16 p-4 sm:p-6 lg:p-8 bg-slate-950">
      <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
      <div className="max-w-7xl mx-auto">
        {/* Profile Header */}
        <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-2xl p-8 mb-8 overflow-hidden shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-purple-600/5" />

          <div className="relative">
            <div className="flex items-center gap-8">
              {/* Avatar Section */}
              <div className="flex flex-col items-center">
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300" />
                  <div className="relative w-24 h-24 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full flex items-center justify-center text-3xl border-3 border-card-dark shadow-xl group-hover:scale-105 transition-transform duration-300">
                    🎮
                  </div>
                </div>
              </div>

              {/* Profile Info Section - Next to Avatar */}
              <div className="flex-1">
                <h1 className="text-2xl xl:text-3xl font-black bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent mb-3">
                  {userData.in_game_name ||
                    `${userData.first_name} ${userData.last_name}`}
                </h1>
                <div className="flex flex-wrap gap-4 text-xs text-secondary-text">
                  {/* <div className="flex items-center gap-1">
                    <Calendar className="w-3 h-3 text-accent-cyan" />
                    <span>Joined {profileData.joinDate}</span>
                  </div> */}
                  <div className="flex items-center gap-1">
                    <Gamepad2 className="w-3 h-3 text-accent-cyan" />
                    <span>{gamesPlayed} games played</span>
                  </div>
                </div>
              </div>

              {/* Stats Grid - Right Side */}
              {/* <div className="grid grid-cols-1 gap-4 min-w-[200px]"> */}
              <div className="bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/10 backdrop-blur rounded-xl p-4 border border-gaming-gold/30 hover:border-gaming-gold/50 transition-all duration-300 group">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Sparkles className="w-4 h-4 text-gaming-gold" />
                      <span className="text-xs font-medium text-secondary-text">
                        Social Points
                      </span>
                    </div>
                    <div className="text-2xl font-black text-gaming-gold group-hover:scale-105 transition-transform duration-300">
                      <AnimatedCounter value={userData.social_points || 0} />
                    </div>
                  </div>
                  <div className="text-2xl opacity-20 group-hover:opacity-30 transition-opacity">
                    ⭐
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-accent-cyan/20 to-primary-blue/10 backdrop-blur rounded-xl p-4 border border-accent-cyan/30 hover:border-accent-cyan/50 transition-all duration-300 group">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Coins className="w-4 h-4 text-accent-cyan" />
                      <span className="text-xs font-medium text-secondary-text">
                        JQ Points
                      </span>
                    </div>
                    <div className="text-2xl font-black text-accent-cyan group-hover:scale-105 transition-transform duration-300">
                      <AnimatedCounter value={userData.jargon_quest || 0} />
                    </div>
                  </div>
                  <div className="text-2xl opacity-20 group-hover:opacity-30 transition-opacity">
                    💰
                  </div>
                </div>
              </div>
              {/* </div> */}
            </div>
          </div>
        </div>
        <div className="bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6">
          <h3 className="text-xl font-bold mb-6 flex items-center gap-2 text-white">
            <Gamepad2 className="w-5 h-5 text-blue-400" />
            Recent Games
          </h3>
          <div className="space-y-3">
            {lastPlayedGames.map((game, index) => (
              <div
                key={index}
                className="bg-slate-800/50 rounded-xl p-4 border border-slate-700/50 hover:border-slate-600 transition-all"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-2xl shadow-lg shadow-blue-500/25">
                      🎮
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg text-white">
                        {game.name}
                      </h4>
                      <div className="flex items-center gap-4 text-sm text-gray-400">
                        <span>Score: {game.score.toLocaleString()}</span>
                        <span>•</span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-lg font-semibold text-green-400">
                      +{game.score} SP
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesktopProfile;
