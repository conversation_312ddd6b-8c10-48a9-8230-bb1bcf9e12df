import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Play,
  Users,
  Coins,
  Filter,
  Search,
  Star,
  Trophy,
  Zap,
  Target,
  Crown,
  Globe,
  TrendingUp,
  Clock,
  Award,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useWallet } from "@/contexts/WalletContext";
import { useGame } from "@/contexts/GameContext";
import { useGames } from "@/hooks/use-game-api";
import { Game } from "@/types/api";
import styles from "./Games.module.css";
import { gameUrls } from "@/game-data";

const DesktopGames = () => {
  const { isConnected, connectWallet } = useWallet();
  const { openAccountConnectModal, userData, setSelectedGame, isAuthLoading } = useGame();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBlockchain, setSelectedBlockchain] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [sortBy, setSortBy] = useState("popular");

  // Fetch games from API
  const { data: apiGames, isLoading, error } = useGames();

  // Transform API games to match the expected format
  const games =
    apiGames?.map((game: Game, index: number) => ({
      id: game._id,
      gameId: game.game_id,
      numericId: index + 1, // For sorting purposes
      title: game.title,
      description: game.description,
      image: game.image,
      blockchain: game.blockchain,
      category: game.category,
      playCount: 10000,
      rewards: `${game.min_reward}-${game.max_reward} JQ`,
      difficulty: game.difficulty,
      featured: game.featured,
      trending: game.trending,
      gameUrl: gameUrls[game.game_id],
    })) || [];

  const blockchains = ["all", "BSC", "SOL"];
  const categories = ["all", "Endless Runner", "Puzzle"];

  const filteredGames = games.filter((game) => {
    const matchesSearch =
      game.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      game.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBlockchain =
      selectedBlockchain === "all" || game.blockchain === selectedBlockchain;
    const matchesCategory =
      selectedCategory === "all" || game.category === selectedCategory;

    return matchesSearch && matchesBlockchain && matchesCategory;
  });

  const sortedGames = [...filteredGames].sort((a, b) => {
    switch (sortBy) {
      case "popular":
        return b.playCount - a.playCount;
      case "newest":
        return (b.numericId || 0) - (a.numericId || 0);
      case "rewards":
        return (
          parseInt(b.rewards.split("-")[1]) - parseInt(a.rewards.split("-")[1])
        );
      default:
        return 0;
    }
  });

  const getBlockchainColor = (blockchain: string) => {
    const colors = {
      BSC: "bsc-badge",
      SOL: "sol-badge",
      ETH: "eth-badge",
      AVAX: "avax-badge",
      MATIC: "matic-badge",
    };
    return colors[blockchain as keyof typeof colors] || "blockchain-badge";
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      Easy: "bg-success-green/20 text-success-green border-success-green/30",
      Medium:
        "bg-legendary-orange/20 text-legendary-orange border-legendary-orange/30",
      Hard: "bg-health-red/20 text-health-red border-health-red/30",
    };
    return colors[difficulty as keyof typeof colors] || "";
  };

  // Handle game selection
  const handleGameSelect = (game: any) => {
    // Convert the local game format back to API format for context
    const apiGame: Game = {
      _id: game.id,
      game_id: game.gameId,
      title: game.title,
      description: game.description,
      image: game.image,
      blockchain: game.blockchain,
      category: game.category,
      min_reward: parseInt(game.rewards.split("-")[0]),
      max_reward: parseInt(game.rewards.split("-")[1].replace(" JQ", "")),
      difficulty: game.difficulty,
      featured: game.featured,
      trending: game.trending,
      game_url: game.gameUrl,
    };
    setSelectedGame(apiGame);
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-slate-950">
      {/* Enhanced Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
        <div className="absolute inset-0 digital-grid opacity-10"></div>

        {/* Floating gaming elements */}
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="absolute opacity-5 pointer-events-none"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float-advanced ${
                6 + Math.random() * 4
              }s ease-in-out infinite`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          >
            <div
              className="w-24 h-24 rounded-full"
              style={{
                background: `radial-gradient(circle, ${
                  ["#06B6D4", "#8B5CF6", "#F59E0B"][i % 3]
                }20, transparent 70%)`,
              }}
            />
          </div>
        ))}
      </div>

      <div className="relative z-10 pt-24 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced Filters */}
          <div className="p-6 mb-12 transition-all duration-300">
            <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="lg:col-span-2">
                <div className="relative group">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-text group-focus-within:text-accent-cyan transition-colors duration-200" />
                  <Input
                    placeholder="Search epic games..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 h-12 bg-surface-dark/80 border-border-light/50 text-primary-text placeholder:text-muted-text focus:border-accent-cyan focus:ring-accent-cyan rounded-xl"
                  />
                </div>
              </div>

              <Select
                value={selectedBlockchain}
                onValueChange={setSelectedBlockchain}
              >
                <SelectTrigger className="h-12 bg-surface-dark/80 border-border-light/50 text-primary-text focus:border-accent-cyan rounded-xl">
                  <SelectValue placeholder="Blockchain" />
                </SelectTrigger>
                <SelectContent className="bg-card-dark border-border-light/50">
                  {blockchains.map((blockchain) => (
                    <SelectItem
                      key={blockchain}
                      value={blockchain}
                      className="text-primary-text hover:bg-surface-dark"
                    >
                      {blockchain === "all" ? "All Chains" : blockchain}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger className="h-12 bg-surface-dark/80 border-border-light/50 text-primary-text focus:border-accent-cyan rounded-xl">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent className="bg-card-dark border-border-light/50">
                  {categories.map((category) => (
                    <SelectItem
                      key={category}
                      value={category}
                      className="text-primary-text hover:bg-surface-dark"
                    >
                      {category === "all" ? "All Genres" : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="h-12 bg-surface-dark/80 border-border-light/50 text-primary-text focus:border-accent-cyan rounded-xl">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent className="bg-card-dark border-border-light/50">
                  <SelectItem
                    value="popular"
                    className="text-primary-text hover:bg-surface-dark"
                  >
                    Most Popular
                  </SelectItem>
                  <SelectItem
                    value="rating"
                    className="text-primary-text hover:bg-surface-dark"
                  >
                    Highest Rated
                  </SelectItem>
                  <SelectItem
                    value="newest"
                    className="text-primary-text hover:bg-surface-dark"
                  >
                    Newest
                  </SelectItem>
                  <SelectItem
                    value="rewards"
                    className="text-primary-text hover:bg-surface-dark"
                  >
                    Best Rewards
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-20">
              <div className="text-center">
                <Loader2 className="h-12 w-12 animate-spin text-accent-cyan mx-auto mb-4" />
                <p className="text-xl text-secondary-text">
                  Loading epic games...
                </p>
                <p className="text-sm text-muted-text mt-2">
                  Preparing your gaming universe
                </p>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="flex items-center justify-center py-20">
              <div className="text-center max-w-md">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-primary-text mb-2">
                  Unable to Load Games
                </h3>
                <p className="text-secondary-text mb-4">
                  We're having trouble connecting to our game servers. Please
                  check your connection and try again.
                </p>
                <Button
                  onClick={() => window.location.reload()}
                  className="bg-gradient-to-r from-accent-cyan to-xp-purple"
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {/* Enhanced All Games Section */}
          {!isLoading && !error && (
            <div>
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl md:text-4xl font-black">
                  <span className="bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
                    All Games
                  </span>
                  <span className="text-secondary-text text-lg ml-3">
                    ({sortedGames.length})
                  </span>
                </h2>
                <div className="flex items-center gap-2 text-sm text-secondary-text">
                  <Award className="w-4 h-4 text-accent-cyan" />
                  <span>Endless Adventures Await</span>
                </div>
              </div>

              {sortedGames.length === 0 ? (
                <div className="bg-gradient-to-br from-card-dark/80 to-surface-dark/60 backdrop-blur-xl border border-border-light/30 rounded-2xl text-center py-16">
                  <div className="text-6xl mb-4">🎮</div>
                  <p className="text-secondary-text text-xl mb-2">
                    No games found
                  </p>
                  <p className="text-muted-text">
                    Try adjusting your search criteria
                  </p>
                </div>
              ) : (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {sortedGames.map((game, index) => (
                    <div
                      key={game.id}
                      className="group relative bg-gradient-to-br from-card-dark/80 to-surface-dark/60 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 hover:scale-105 hover:border-accent-cyan/50 transition-all duration-500 hover:shadow-xl overflow-hidden"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      {/* Status indicators */}
                      <div className="absolute top-4 right-4 flex flex-col gap-1">
                        {game.trending && (
                          <Badge className="bg-success-green/20 text-success-green border-success-green/30 text-xs">
                            <TrendingUp className="w-2 h-2 mr-1" />
                            Hot
                          </Badge>
                        )}
                      </div>

                      {/* Game icon */}
                      <div className="text-center mb-6">
                        <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
                          {game.image}
                        </div>
                      </div>

                      {/* Game info */}
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-lg font-bold group-hover:text-accent-cyan transition-colors duration-300">
                          {game.title}
                        </h3>
                        <span
                          className={`${
                            styles[getBlockchainColor(game.blockchain)]
                          } px-2 py-1 rounded-full text-xs font-medium`}
                        >
                          {game.blockchain}
                        </span>
                      </div>

                      <p className="text-secondary-text text-sm mb-4 leading-relaxed line-clamp-3 group-hover:text-primary-text transition-colors duration-300">
                        {game.description}
                      </p>

                      {/* Badges */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        <Badge
                          className={`${getDifficultyColor(
                            game.difficulty
                          )} border text-xs`}
                        >
                          {game.difficulty}
                        </Badge>
                        <Badge className="bg-xp-purple/20 text-xp-purple border-xp-purple/30 text-xs">
                          {game.category}
                        </Badge>
                      </div>

                      {/* Stats grid */}
                      <div className="grid grid-cols-2 gap-4 mb-6 text-xs">
                        <div className="text-center bg-surface-dark/40 rounded-lg p-2">
                          <div className="flex items-center justify-center space-x-1 mb-1">
                            <Coins className="h-3 w-3 text-gaming-gold" />
                            <span className="text-gaming-gold font-medium">
                              {game.rewards}
                            </span>
                          </div>
                          <p className="text-xs text-secondary-text">Rewards</p>
                        </div>
                        <div className="text-center bg-surface-dark/40 rounded-lg p-2">
                          <div className="flex items-center justify-center space-x-1 mb-1">
                            <Play className="h-3 w-3 text-accent-cyan" />
                            <span className="text-accent-cyan font-medium">
                              {game.playCount.toLocaleString()}
                            </span>
                          </div>
                          <p className="text-xs text-secondary-text">
                            Play Count
                          </p>
                        </div>
                      </div>

                      {/* Button */}
                      {userData ? (
                        <Button
                          asChild
                          className="w-full bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-medium hover:scale-105 transition-all duration-300"
                        >
                          <Link
                            to={`/game/${game.gameId}`}
                            onClick={() => handleGameSelect(game)}
                          >
                            <Play className="mr-2 h-4 w-4" />
                            Play Now
                          </Link>
                        </Button>
                      ) : (
                        <Button
                          onClick={openAccountConnectModal}
                          disabled={isAuthLoading}
                          className="w-full bg-gradient-to-r from-surface-dark to-border-light text-primary-text font-medium hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isAuthLoading ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Connecting...
                            </>
                          ) : (
                            <>
                              <Globe className="mr-2 h-4 w-4" />
                              Connect to Play
                            </>
                          )}
                        </Button>
                      )}

                      {/* Subtle hover overlay */}
                      <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/5 to-xp-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none"></div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DesktopGames;
