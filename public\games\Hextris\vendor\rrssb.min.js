+function(t,e,r){"use strict";var s={calc:!1};e.fn.rrssb=function(t){var s=e.extend({description:r,emailAddress:r,emailBody:r,emailSubject:r,image:r,title:r,url:r},t);for(var i in s)s.hasOwnProperty(i)&&s[i]!==r&&(s[i]=a(s[i]));s.url!==r&&(e(this).find(".rrssb-facebook a").attr("href","https://www.facebook.com/sharer/sharer.php?u="+s.url),e(this).find(".rrssb-tumblr a").attr("href","http://tumblr.com/share/link?url="+s.url+(s.title!==r?"&name="+s.title:"")+(s.description!==r?"&description="+s.description:"")),e(this).find(".rrssb-linkedin a").attr("href","http://www.linkedin.com/shareArticle?mini=true&url="+s.url+(s.title!==r?"&title="+s.title:"")+(s.description!==r?"&summary="+s.description:"")),e(this).find(".rrssb-twitter a").attr("href","http://twitter.com/home?status="+(s.description!==r?s.description:"")+"%20"+s.url),e(this).find(".rrssb-hackernews a").attr("href","https://news.ycombinator.com/submitlink?u="+s.url+(s.title!==r?"&text="+s.title:"")),e(this).find(".rrssb-reddit a").attr("href","http://www.reddit.com/submit?url="+s.url+(s.description!==r?"&text="+s.description:"")+(s.title!==r?"&title="+s.title:"")),e(this).find(".rrssb-googleplus a").attr("href","https://plus.google.com/share?url="+(s.description!==r?s.description:"")+"%20"+s.url),e(this).find(".rrssb-pinterest a").attr("href","http://pinterest.com/pin/create/button/?url="+s.url+(s.image!==r?"&amp;media="+s.image:"")+(s.description!==r?"&amp;description="+s.description:"")),e(this).find(".rrssb-pocket a").attr("href","https://getpocket.com/save?url="+s.url),e(this).find(".rrssb-github a").attr("href",s.url)),s.emailAddress!==r&&e(this).find(".rrssb-email a").attr("href","mailto:"+s.emailAddress+"?"+(s.emailSubject!==r?"subject="+s.emailSubject:"")+(s.emailBody!==r?"&amp;body="+s.emailBody:""))};var i=function(){var t=e("<div>"),r=["calc","-webkit-calc","-moz-calc"];e("body").append(t);for(var i=0;i<r.length;i++)if(t.css("width",r[i]+"(1px)"),1===t.width()){s.calc=r[i];break}t.remove()},a=function(t){if(t!==r&&null!==t){if(null===t.match(/%[0-9a-f]{2}/i))return encodeURIComponent(t);t=decodeURIComponent(t),a(t)}},n=function(){e(".rrssb-buttons").each(function(t){var r=e(this),s=e("li:visible",r),i=s.length,a=100/i;s.css("width",a+"%").attr("data-initwidth",a)})},l=function(){e(".rrssb-buttons").each(function(t){var r=e(this),s=r.width(),i=e("li",r).not(".small").first().width();i>170&&e("li.small",r).length<1?r.addClass("large-format"):r.removeClass("large-format"),200>s?r.removeClass("small-format").addClass("tiny-format"):r.removeClass("tiny-format")})},o=function(){e(".rrssb-buttons").each(function(t){var r=e(this),s=e("li",r),i=s.filter(".small"),a=0,n=0,l=i.first(),o=parseFloat(l.attr("data-size"))+55,c=i.length;if(c===s.length){var d=42*c,m=r.width();m>d+o&&(r.removeClass("small-format"),i.first().removeClass("small"),h())}else{s.not(".small").each(function(t){var r=e(this),s=parseFloat(r.attr("data-size"))+55,i=parseFloat(r.width());a+=i,n+=s});var u=a-n;u>o&&(l.removeClass("small"),h())}})},c=function(t){e(".rrssb-buttons").each(function(t){var r=e(this),s=e("li",r);e(s.get().reverse()).each(function(t,r){var i=e(this);if(i.hasClass("small")===!1){var a=parseFloat(i.attr("data-size"))+55,n=parseFloat(i.width());if(a>n){var l=s.not(".small").last();e(l).addClass("small"),h()}}--r||o()})}),t===!0&&m(h)},h=function(){e(".rrssb-buttons").each(function(t){var r,i,a,l,o,c=e(this),h=e("li",c),d=h.filter(".small"),m=d.length;m>0&&m!==h.length?(c.removeClass("small-format"),d.css("width","42px"),a=42*m,r=h.not(".small").length,i=100/r,o=a/r,s.calc===!1?(l=(c.innerWidth()-1)/r-o,l=Math.floor(1e3*l)/1e3,l+="px"):l=s.calc+"("+i+"% - "+o+"px)",h.not(".small").css("width",l)):m===h.length?(c.addClass("small-format"),n()):(c.removeClass("small-format"),n())}),l()},d=function(){e(".rrssb-buttons").each(function(t){e(this).addClass("rrssb-"+(t+1))}),i(),n(),e(".rrssb-buttons li .rrssb-text").each(function(t){var r=e(this),s=r.width();r.closest("li").attr("data-size",s)}),c(!0)},m=function(t){e(".rrssb-buttons li.small").removeClass("small"),c(),t()},u=function(e,s,i,a){var n=t.screenLeft!==r?t.screenLeft:screen.left,l=t.screenTop!==r?t.screenTop:screen.top,o=t.innerWidth?t.innerWidth:document.documentElement.clientWidth?document.documentElement.clientWidth:screen.width,c=t.innerHeight?t.innerHeight:document.documentElement.clientHeight?document.documentElement.clientHeight:screen.height,h=o/2-i/2+n,d=c/3-a/3+l,m=t.open(e,s,"scrollbars=yes, width="+i+", height="+a+", top="+d+", left="+h);t.focus&&m.focus()},f=function(){var t={};return function(e,r,s){s||(s="Don't call this twice without a uniqueId"),t[s]&&clearTimeout(t[s]),t[s]=setTimeout(e,r)}}();e(document).ready(function(){e(".rrssb-buttons a.popup").on("click",function(t){var r=e(this);u(r.attr("href"),r.find(".rrssb-text").html(),580,470),t.preventDefault()}),e(t).resize(function(){m(h),f(function(){m(h)},200,"finished resizing")}),d()}),t.rrssbInit=d}(window,$);