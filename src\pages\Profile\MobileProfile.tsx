import { useState } from "react";
import {
  <PERSON>pad2,
  <PERSON><PERSON><PERSON>,
  Co<PERSON>,
  Loader2,
  Shield,
} from "lucide-react";
import { useGame } from "@/contexts/GameContext";

const MobileProfile = () => {
  const { userData, gameStats, lastPlayedGames, isAuthLoading } = useGame();

  const gamesPlayed = Object.keys(gameStats || {})
    .map((gameId) => gameStats[gameId])
    .reduce((acc, stats) => acc + stats.games_played, 0);

  const AnimatedCounter = ({ value, decimals = 0, prefix = "" }) => {
    return (
      <span>
        {prefix}
        {value.toLocaleString(undefined, {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals,
        })}
      </span>
    );
  };

  if (isAuthLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-slate-950 pt-24">
        <div className="relative bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-xl p-6 max-w-sm mx-auto shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/5 to-xp-purple/5 rounded-xl pointer-events-none" />
          <div className="relative text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-accent-cyan/25">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
            <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Loading Profile
            </h2>
            <p className="text-secondary-text text-sm leading-relaxed">
              Please wait while we fetch your profile data.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-slate-950 pt-24">
        <div className="relative bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-xl p-6 max-w-sm mx-auto shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/5 to-xp-purple/5 rounded-xl pointer-events-none" />
          <div className="relative text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-accent-cyan/25">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Account
            </h2>
            <p className="text-secondary-text text-sm leading-relaxed">
              Please connect your account to view your gaming profile and track
              your achievements.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-24 pb-8 px-4 bg-slate-950">
      <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
      <div className="relative z-10 max-w-2xl mx-auto">
        {/* Profile Header - Mobile Optimized */}
        <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-2xl p-6 mb-6 overflow-hidden shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-purple-600/5" />

          <div className="relative">
            {/* Avatar Section - Centered for mobile */}
            <div className="flex flex-col items-center mb-6">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full blur-lg opacity-30 transition-opacity duration-300" />
                <div className="relative w-20 h-20 bg-gradient-to-br from-accent-cyan to-xp-purple rounded-full flex items-center justify-center text-2xl border-3 border-card-dark shadow-xl transition-transform duration-300">
                  🎮
                </div>
              </div>
            </div>

            {/* Profile Info Section - Centered */}
            <div className="text-center mb-6">
              <h1 className="text-2xl font-black bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent mb-3">
                {userData.in_game_name ||
                  `${userData.first_name} ${userData.last_name}`}
              </h1>
              <div className="flex items-center justify-center gap-2 text-sm text-secondary-text">
                <Gamepad2 className="w-4 h-4 text-accent-cyan" />
                <span>{gamesPlayed} games played</span>
              </div>
            </div>

            {/* Stats Grid - 2x2 for mobile */}
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/10 backdrop-blur rounded-xl p-4 border border-gaming-gold/30 transition-all duration-300">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Sparkles className="w-4 h-4 text-gaming-gold" />
                    <span className="text-xs font-medium text-secondary-text">
                      Social Points
                    </span>
                  </div>
                  <div className="text-xl font-black text-gaming-gold">
                    <AnimatedCounter value={userData.social_points || 0} />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-accent-cyan/20 to-primary-blue/10 backdrop-blur rounded-xl p-4 border border-accent-cyan/30 transition-all duration-300">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Coins className="w-4 h-4 text-accent-cyan" />
                    <span className="text-xs font-medium text-secondary-text">
                      JQ Points
                    </span>
                  </div>
                  <div className="text-xl font-black text-accent-cyan">
                    <AnimatedCounter value={userData.jargon_quest || 0} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Games - Mobile Optimized */}
        <div className="bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-4">
          <h3 className="text-lg font-bold mb-4 flex items-center gap-2 text-white">
            <Gamepad2 className="w-5 h-5 text-blue-400" />
            Recent Games
          </h3>
          <div className="space-y-3">
            {lastPlayedGames.map((game, index) => (
              <div
                key={index}
                className="bg-slate-800/50 rounded-xl p-4 border border-slate-700/50 transition-all"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-xl shadow-lg shadow-blue-500/25">
                    🎮
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-base text-white mb-1">
                      {game.name}
                    </h4>
                    <div className="text-sm text-gray-400">
                      Score: {game.score.toLocaleString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-base font-semibold text-green-400">
                      +{game.score} SP
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileProfile;
