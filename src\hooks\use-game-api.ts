import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { GameApiService } from "@/services/game-api";
import { Game, UserStats ,SendMessageResponse, AddReactionResponse, DeleteMessageResponse} from "@/types/api";
// Query Keys
export const QUERY_KEYS = {
  GAMES: ["games"] as const,
  CHANNELS: ["channels"] as const,
  MESSAGES: ["messages"] as const,
  CHANNEL_MESSAGES: (channelId: string) => ["channel-messages", channelId] as const,
  CONNECT_ACCOUNT: (token: string, tg_id: string) =>
    ["connect-account", token, tg_id] as const,
  CONNECT_ACCOUNT_WITH_AUTH: (authToken: string) =>
    ["connect-account-with-auth", authToken] as const,
} as const;

// Hook to fetch games
export const useGames = () => {
  return useQuery({
    queryKey: QUERY_KEYS.GAMES,
    queryFn: GameApiService.getGames,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

// Hook to fetch channels
export const useChannels = () => {
  return useQuery({
    queryKey: QUERY_KEYS.CHANNELS,
    queryFn: GameApiService.getChannels,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

// Hook to connect account with token and tg_id
export const useConnectAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ token, tg_id }: { token: string; tg_id: string }) =>
      GameApiService.connectAccount(token, parseInt(tg_id)),
    onSuccess: (data) => {
      // Cache the connection data
      queryClient.setQueryData(
        QUERY_KEYS.CONNECT_ACCOUNT(data.user_data._id, "connected"),
        data
      );
    },
    onError: (error) => {
      console.error("Failed to connect account:", error);
    },
  });
};

// Hook to connect account with existing auth token
export const useConnectAccountWithAuth = (
  authToken: string,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CONNECT_ACCOUNT_WITH_AUTH(authToken),
    queryFn: () => GameApiService.connectAccountWithAuth(authToken),
    enabled: enabled && !!authToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: false, // Don't retry if auth token is invalid
  });
};

// Hook to update score
export const useUpdateScore = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      gameId,
      score,
      authToken,
    }: {
      gameId: string;
      score: number;
      authToken: string;
    }) => GameApiService.updateScore(gameId, score, authToken),
    onSuccess: (data, variables) => {
      // Invalidate connect account with auth to refresh game stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.CONNECT_ACCOUNT_WITH_AUTH(variables.authToken),
      });
    },
    onError: (error) => {
      console.error("Failed to update score:", error);
    },
  });
};

export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      channelId,
      content,
      authToken,
      messageType = "text",
      replyTo,
    }: {
      channelId: string;
      content: string;
      authToken: string;
      messageType?: "text" | "image" | "file" | "system";
      replyTo?: string;
    }) => GameApiService.sendMessage(channelId, content, authToken, messageType, replyTo),
    onSuccess: (data, variables) => {
      // // Invalidate channel messages to refresh the message list
      // queryClient.invalidateQueries({
      //   queryKey: QUERY_KEYS.CHANNEL_MESSAGES(variables.channelId),
      // });
      
      // Also invalidate general messages cache
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.MESSAGES,
      });
    },
    onError: (error) => {
      console.error("Failed to send message:", error);
    },
  });
};

// Hook to add reaction to message
export const useAddReaction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      channelId,
      messageId,
      emoji,
      authToken,
    }: {
      channelId: string;
      messageId: string;
      emoji: string;
      authToken: string;
    }) => GameApiService.addReaction(channelId, messageId, emoji, authToken),
    onSuccess: (data, variables) => {
      // // Invalidate channel messages to refresh reactions
      // queryClient.invalidateQueries({
      //   queryKey: QUERY_KEYS.CHANNEL_MESSAGES(variables.channelId),
      // });
      
      // Also invalidate general messages cache
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.MESSAGES,
      });
    },
    onError: (error) => {
      console.error("Failed to add reaction:", error);
    },
  });
};

export const useChannelMessages = (channelId: string, authToken: string, options = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.CHANNEL_MESSAGES(channelId),
    queryFn: () => {
      if (!authToken) {
        throw new Error('No auth token available');
      }
      return GameApiService.getChannelMessages(channelId, authToken);
    },
    enabled: Boolean(channelId && authToken && authToken.length > 0),
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
    refetchInterval: Boolean(authToken && authToken.length > 0) ? 30 * 1000 : false,
    retry: (failureCount, error) => {
      // Don't retry if it's an auth error
      if (error.message.includes('auth') || !authToken) {
        return false;
      }
      return failureCount < 3;
    },
    ...options,
  });
};

// // Hook to delete message
// export const useDeleteMessage = () => {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: ({
//       channelId,
//       messageId,
//       authToken,
//     }: {
//       channelId: string;
//       messageId: string;
//       authToken: string;
//     }) => GameApiService.deleteMessage(channelId, messageId, authToken),
//     onSuccess: (data, variables) => {
//       // Invalidate channel messages to refresh the message list
//       queryClient.invalidateQueries({
//         queryKey: QUERY_KEYS.CHANNEL_MESSAGES(variables.channelId),
//       });
      
//       // Also invalidate general messages cache
//       queryClient.invalidateQueries({
//         queryKey: QUERY_KEYS.MESSAGES,
//       });
//     },
//     onError: (error) => {
//       console.error("Failed to delete message:", error);
//     },
//   });
// };
