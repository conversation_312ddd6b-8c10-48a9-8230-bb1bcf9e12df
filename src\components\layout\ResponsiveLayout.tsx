import { useGame } from "@/contexts/GameContext";
import { useIsMobile } from "@/hooks/use-mobile";
import Header from "./Header";
import MobileHeader from "./MobileHeader";
import { AccountConnectModal } from "../modals/AccountConnectModal";
import { MobileAccountConnectModal } from "../modals/MobileAccountConnectModal";

interface ResponsiveLayoutProps {
  children: React.ReactNode;
}

const ResponsiveLayout = ({ children }: ResponsiveLayoutProps) => {
  const { showAccountConnectModal, setShowAccountConnectModal } = useGame();
  const isMobile = useIsMobile();

  return (
    <div className="min-h-screen bg-dark-bg">
      {/* Conditionally render appropriate header */}
      {isMobile ? <MobileHeader /> : <Header />}
      
      {/* Main content */}
      {children}

      {/* Account Connect Modal - conditionally render mobile or desktop version */}
      {isMobile ? (
        <MobileAccountConnectModal
          isOpen={showAccountConnectModal}
          onClose={() => setShowAccountConnectModal(false)}
        />
      ) : (
        <AccountConnectModal
          isOpen={showAccountConnectModal}
          onClose={() => setShowAccountConnectModal(false)}
        />
      )}
    </div>
  );
};

export default ResponsiveLayout;
