import { ApiResponse, ApiError } from "@/types/api";

// API Configuration
const API_BASE_URL = `${import.meta.env.VITE_API_BASE_URL}/api/v1/gaming-platform`;
const API_KEY = import.meta.env.VITE_API_KEY;

// Custom error class for API errors
export class ApiClientError extends Error {
  constructor(message: string, public status: number, public response?: any) {
    super(message);
    this.name = "ApiClientError";
  }
}

// API Client class
export class ApiClient {
  private baseURL: string;
  private apiKey: string;

  constructor(baseURL: string = API_BASE_URL, apiKey: string = API_KEY) {
    this.baseURL = baseURL.replace(/\/$/, ""); // Remove trailing slash
    this.apiKey = apiKey;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    authToken?: string
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "x-api-key": this.apiKey,
    };

    // Add authorization header if authToken is provided
    if (authToken) {
      headers["authorization"] = `Bearer ${authToken}`;
    }

    const config: RequestInit = {
      headers: {
        ...headers,
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorData: any = null;

        try {
          errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch {
          // If we can't parse the error response, use the default message
        }

        throw new ApiClientError(errorMessage, response.status, errorData);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof ApiClientError) {
        throw error;
      }

      // Handle network errors or other fetch errors
      throw new ApiClientError(
        error instanceof Error ? error.message : "An unknown error occurred",
        0
      );
    }
  }

  // GET request
  async get<T>(endpoint: string, authToken?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET" }, authToken);
  }

  // POST request
  async post<T>(endpoint: string, data?: any, authToken?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    }, authToken);
  }

  // PUT request
  async put<T>(endpoint: string, data?: any, authToken?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    }, authToken);
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any, authToken?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    }, authToken);
  }

  // DELETE request
  async delete<T>(endpoint: string, authToken?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" }, authToken);
  }
}

// Create and export a default instance
export const apiClient = new ApiClient();
