import React, { useRef, useState } from 'react';
import PianoTiles from './PianoTiles';
import StackIt from './StackIt';
import { useGame } from '@/contexts/GameContext';

// Extend the Window interface to include userData and initUserData
declare global {
  interface Window {
    userData?: any;
    initUserData?: (userData: any) => void;
  }
}

interface GameRendererProps {
  gameId: string;
  gameUrl?: string;
  gameTitle: string;
  isFullscreen?: boolean;
  onScoreUpdate?: (score: number) => void;
  onGameEnd?: (finalScore: number) => void;
  onLoad?: () => void;
}

const GameRenderer: React.FC<GameRendererProps> = ({
  gameId,
  gameUrl,
  gameTitle,
  isFullscreen = false,
  onScoreUpdate,
  onGameEnd,
  onLoad
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isGameLoaded, setIsGameLoaded] = useState(false);
  const { userData } = useGame();

  console.log(userData);


  const handleIframeLoad = () => {
    setIsGameLoaded(true);

    // Pass userData to iframe after it loads
    if (iframeRef.current && userData && gameId == 'jq_puzzle') {
      try {
        // Direct access method
        const iframeWindow = iframeRef.current.contentWindow;
        if (iframeWindow) {
          // Set userData on iframe's window object
          iframeWindow.userData = userData?.total_points;
          // iframeWindow.userData = 18000000000;
        }
      } catch (error) {
        console.error('Error passing userData to iframe:', error);
      }
    }

    onLoad?.();
  };

  // Render React component games
  const renderComponentGame = () => {
    switch (gameId) {
      case 'jargon_tiles':
        return (
          <PianoTiles
            onScoreUpdate={onScoreUpdate}
            onGameEnd={onGameEnd}
          />
        );
      case 'stackit':
        return (
          <StackIt
            onScoreUpdate={onScoreUpdate}
            onGameEnd={onGameEnd}
          />
        );
      default:
        return null;
    }
  };

  // Check if this is a component-based game
  const isComponentGame = ['jargon_tiles', 'stackit'].includes(gameId) || gameUrl === 'COMPONENT';

  if (isComponentGame) {
    return (
      <div className={`${isFullscreen ? 'w-full h-full fixed inset-0 z-50' : 'flex-1 h-full'}`}>
        {renderComponentGame()}
      </div>
    );
  }

  // Render iframe-based games
  return (
    <div className={`relative ${isFullscreen ? 'w-full h-full' : 'flex-1'}`}>
      {/* Loading indicator */}
      {!isGameLoaded && (
        <div className="absolute inset-0 bg-deep-space flex items-center justify-center z-20">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-accent-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-accent-cyan font-medium">
              Loading Game...
            </p>
            <p className="text-secondary-text text-sm mt-2">
              Click on the game area once loaded to start playing
            </p>
          </div>
        </div>
      )}

      {/* Game iframe */}
      <iframe
        ref={iframeRef}
        src={gameUrl}
        className={`w-full h-full border-0 bg-black ${isFullscreen ? 'object-cover' : ''
          }`}
        title={gameTitle}
        allow="fullscreen; gamepad; microphone; camera"
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-pointer-lock"
        onLoad={handleIframeLoad}
        tabIndex={0}
        style={{
          ...(isFullscreen && {
            width: "100vw",
            height: "100vh",
            objectFit: "cover",
          }),
        }}
      />
    </div>
  );
};

export default GameRenderer;
