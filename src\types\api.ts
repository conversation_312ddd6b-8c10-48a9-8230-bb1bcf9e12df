// API Response Types
export interface ApiResponse<T> {
  message: string;
  payload: T;
}

// Game Types
export interface Game {
  _id: string;
  game_id: string;
  title: string;
  description: string;
  image: string;
  blockchain: string;
  category: string;
  min_reward: number;
  max_reward: number;
  difficulty: string;
  featured: boolean;
  trending: boolean;
  game_url: string;
}

export interface Channel {
  _id: string;
  channel_id: string;
  channel_name: string;
  channel_type: "text" | "voice" | "announcement" | "gaming" | "trading" | "support";
  member_count: number;
  online_count: number;
  only_admin: boolean;
  only_moderators: boolean;
  pinned_messages: string[];
}

export interface GamesResponse {
  games: Game[];
}

export interface ChannelsResponse {
  channels: Channel[];
}

export interface UserData {
  _id: string;
  wallet_address?: string;
  in_game_name?: string;
  first_name: string;
  last_name: string;
  social_points?: number;
  total_points: number;
  jargon_quest?: number;
  is_admin: boolean;
  is_moderator: boolean;
}

// User Stats Types
export interface UserStats {
  best_score: number;
  games_played: number;
  total_score: number;
}

// Last Played Games Types
export interface LastPlayedGame {
  name: string;
  score: number;
  time_stamp: Date;
}

// Connect Account Types
export interface ConnectAccountRequest {
  token: string;
  tg_id: number;
}

export interface ConnectAccountResponse {
  user_data: UserData;
  auth_token: string;
  game_stats: Record<string, UserStats>;
  last_4_played_games: LastPlayedGame[];
}

// Update Score Types (now uses auth header instead of credentials in body)
export interface UpdateScoreRequest {
  game_id: string;
  score: number;
}

export interface UpdateScoreResponse extends UserStats { }

// Error Response Type
export interface ApiError {
  error: string;
}

// Chat Types
export interface SendMessageRequest {
  channel_id: string;
  content: string;
  messageType?: "text" | "image" | "file" | "system";
  replyTo?: string;
}

export interface SendMessageResponse {
  message: Message;
  channel_id: string;
}

export interface AddReactionRequest {
  channel_id: string;
  message_id: string;
  emoji: string;
}

export interface AddReactionResponse {
  message_id: string;
  reactions: Reaction[];
}

export interface DeleteMessageResponse {
  message_id: string;
  channel_id: string;
}

export interface Message {
  message_id: string;
  sender: UserSender;
  content: string;
  message_type: string;
  reactions: Reaction[];
  reply_to?: string;
  edited: boolean;
  edited_at?: Date;
  status: string;
  created_at: Date;
}

export interface ChannelMessagesResponse {
  channel_info: {
    channel_id: string;
    channel_name: string;
    channel_type: string;
    member_count: number;
    online_count: number;
    only_admin: boolean;
    only_moderators: boolean;
  };
  messages: Message[];
  pagination: {
    currentPage: number;
    totalMessages: number;
    hasMore: boolean;
  };
}

export interface Message {
  message_id: string;
  sender: {
    user_id: string;
    username: string;
    level: number;
    avatar: string;
    is_admin: boolean;
    is_moderator: boolean;
  };
  content: string;
  message_type: string;
  reactions: {
    emoji: string;
    count: number;
    users: string[];
  }[];
  reply_to?: string;
  // status: "sent" | "delivered" | "read" | "deleted";
  created_at: Date;
}

export interface UserSender {
  user_id: string;
  username: string;
  level: number;
  avatar: string;
  is_admin: boolean;
  is_moderator: boolean;
}

export interface Reaction {
  emoji: string;
  count: number;
  users: string[];
}

// API Endpoints
export const API_ENDPOINTS = {
  GAMES: "/games",
  CHANNELS: "/channels",
  REACTIONS: '/reactions',
  CONNECT_ACCOUNT: "/connect-account",
  CONNECT_ACCOUNT_WITH_AUTH: "/connect-account-with-auth",
  UPDATE_SCORE: "/update-score",
} as const;
