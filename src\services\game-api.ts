import { apiClient } from '@/lib/api-client';
import {
  API_ENDPOINTS,
  GamesResponse,
  ConnectAccountRequest,
  ConnectAccountResponse,
  UpdateScoreRequest,
  UpdateScoreResponse,
  UserStats,
  ChannelsResponse,
  SendMessageRequest,
  SendMessageResponse,
  AddReactionRequest,
  AddReactionResponse,
  DeleteMessageResponse,
  ChannelMessagesResponse,
} from '@/types/api';

// Game API Service
export class GameApiService {
  /**
   * Fetch all games
   */
  static async getGames() {
    try {
      const response = await apiClient.get<GamesResponse>(API_ENDPOINTS.GAMES);
      return response.payload.games;
    } catch (error) {
      console.error('Error fetching games:', error);
      throw error;
    }
  }

  /**
 * Fetch all games
 */
  static async getChannels() {
    try {
      const response = await apiClient.get<ChannelsResponse>(API_ENDPOINTS.CHANNELS);
      return response.payload.channels;
    } catch (error) {
      console.error('Error fetching channels:', error);
      throw error;
    }
  }

  /**
   * Connect account with token and tg_id (initial connection)
   */
  static async connectAccount(token: string, tg_id: number) {
    try {
      const requestData: ConnectAccountRequest = {
        token,
        tg_id,
      };

      const response = await apiClient.post<ConnectAccountResponse>(
        API_ENDPOINTS.CONNECT_ACCOUNT,
        requestData
      );

      return response.payload;
    } catch (error) {
      console.error('Error connecting account:', error);
      throw error;
    }
  }

  /**
   * Connect account with existing auth token (automatic connection)
   */
  static async connectAccountWithAuth(authToken: string) {
    try {
      const response = await apiClient.post<ConnectAccountResponse>(
        API_ENDPOINTS.CONNECT_ACCOUNT_WITH_AUTH,
        undefined, // No body needed
        authToken
      );

      return response.payload;
    } catch (error) {
      console.error('Error connecting account with auth:', error);
      throw error;
    }
  }

  /**
   * Update user score for a specific game
   */
  static async updateScore(gameId: string, score: number, authToken: string) {
    try {
      const requestData: UpdateScoreRequest = {
        game_id: gameId,
        score: score,
      };

      const response = await apiClient.patch<UpdateScoreResponse>(
        API_ENDPOINTS.UPDATE_SCORE,
        requestData,
        authToken
      );

      return response.payload;
    } catch (error) {
      console.error('Error updating score:', error);
      throw error;
    }
  }

  /**
 * Send message to channel
 */
  static async sendMessage(
    channelId: string,
    content: string,
    authToken: string,
    messageType: "text" | "image" | "file" | "system" = "text",
    replyTo?: string
  ) {
    try {
      const requestData: SendMessageRequest = {
        channel_id: channelId,
        content,
        messageType,
        replyTo,
      };

      const response = await apiClient.post<SendMessageResponse>(
        `${API_ENDPOINTS.CHANNELS}/${channelId}/messages`,
        requestData,
        authToken
      );

      return response.payload;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Add reaction to message
   */
  static async addReaction(
    channelId: string,
    messageId: string,
    emoji: string,
    authToken: string
  ) {
    try {
      const requestData: AddReactionRequest = {
        channel_id: channelId,
        message_id: messageId,
        emoji,
      };

      const response = await apiClient.post<AddReactionResponse>(
        API_ENDPOINTS.REACTIONS,
        requestData,
        authToken
      );

      return response.payload;
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
  }

  static async getChannelMessages(
    channelId: string,
    authToken: string,
    page: number = 1,
    limit: number = 50
  ) {
    try {
      const response = await apiClient.get<ChannelMessagesResponse>(
        `${API_ENDPOINTS.CHANNELS}/${channelId}/messages?page=${page}&limit=${limit}`,
        authToken
      );

      return response.payload;
    } catch (error) {
      console.error('Error fetching channel messages:', error);
      throw error;
    }
  }


  /**
   * Delete message from channel
   */
  // static async deleteMessage(
  //   channelId: string,
  //   messageId: string,
  //   authToken: string
  // ) {
  //   try {
  //     const response = await apiClient.delete<DeleteMessageResponse>(
  //       `${API_ENDPOINTS.CHANNELS}/${channelId}/messages/${messageId}`,
  //       undefined,
  //       authToken
  //     );

  //     return response.payload;
  //   } catch (error) {
  //     console.error('Error deleting message:', error);
  //     throw error;
  //   }
  // }
}

// Export individual functions for easier importing
export const { getGames, connectAccount, connectAccountWithAuth, updateScore, sendMessage, addReaction, getChannelMessages } = GameApiService;
