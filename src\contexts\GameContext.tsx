import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { Game, LastPlayedGame, UserData, UserStats } from "@/types/api";

// Auth Token storage key
const AUTH_TOKEN_STORAGE_KEY = "jq_auth_token";

interface GameContextType {
  // Auth Token management
  authToken: string | null;
  setAuthToken: (token: string) => void;
  clearAuthToken: () => void;

  // Auth loading state
  isAuthLoading: boolean;
  setIsAuthLoading: (loading: boolean) => void;

  // Account Connect modal
  openAccountConnectModal: () => void;

  // Call connect with auth API
  callConnectWithAuthApi: boolean;
  setCallConnectWithAuthApi: (call: boolean) => void;

  // User data
  userData: UserData | null;
  setUserData: (data: UserData | null) => void;

  // Selected game management
  selectedGame: Game | null;
  setSelectedGame: (game: Game | null) => void;

  // User stats for current game
  currentUserStats: UserStats | null;
  setCurrentUserStats: (stats: UserStats | null) => void;
  gameStats: Record<string, UserStats> | null;
  setGameStats: (stats: Record<string, UserStats> | null) => void;

  // Last played games
  lastPlayedGames: LastPlayedGame[] | null;
  setLastPlayedGames: (games: LastPlayedGame[] | null) => void;

  // Modal state for Account Connect input
  showAccountConnectModal: boolean;
  setShowAccountConnectModal: (show: boolean) => void;

  // Helper functions
  isGameSelected: boolean;
  hasAuthToken: boolean;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

export const useGame = () => {
  const context = useContext(GameContext);
  if (context === undefined) {
    throw new Error("useGame must be used within a GameProvider");
  }
  return context;
};

interface GameProviderProps {
  children: ReactNode;
}

export const GameProvider: React.FC<GameProviderProps> = ({ children }) => {
  const [authToken, setAuthTokenState] = useState<string | null>(null);
  const [isAuthLoading, setIsAuthLoading] = useState<boolean>(false);
  const [callConnectWithAuthApi, setCallConnectWithAuthApi] =
    useState<boolean>(true);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [currentUserStats, setCurrentUserStats] = useState<UserStats | null>(
    null
  );
  const [gameStats, setGameStats] = useState<Record<string, UserStats> | null>(
    null
  );
  const [lastPlayedGames, setLastPlayedGames] = useState<
    LastPlayedGame[] | null
  >(null);
  const [showAccountConnectModal, setShowAccountConnectModal] = useState(false);

  // Load Auth Token from localStorage on mount
  useEffect(() => {
    const storedAuthToken = localStorage.getItem(AUTH_TOKEN_STORAGE_KEY);
    if (storedAuthToken) {
      setAuthTokenState(storedAuthToken);
    }
  }, []);

  const openAccountConnectModal = () => {
    if (!hasAuthToken) setShowAccountConnectModal(true);
  };

  // Set Auth Token and persist to localStorage
  const setAuthToken = (token: string) => {
    setAuthTokenState(token);
    localStorage.setItem(AUTH_TOKEN_STORAGE_KEY, token);
  };

  // Clear Auth Token from state and localStorage
  const clearAuthToken = () => {
    setAuthTokenState(null);
    localStorage.removeItem(AUTH_TOKEN_STORAGE_KEY);
    setUserData(null); // Clear user data when clearing token
    setCurrentUserStats(null); // Clear stats when clearing token
    setGameStats(null); // Clear game stats when clearing token
  };

  // Computed values
  const isGameSelected = selectedGame !== null;
  const hasAuthToken = authToken !== null && authToken.trim() !== "";

  const value: GameContextType = {
    // Auth Token management
    authToken,
    setAuthToken,
    clearAuthToken,

    // Auth loading state
    isAuthLoading,
    setIsAuthLoading,

    // Account Connect modal
    openAccountConnectModal,

    // Call connect with auth API
    callConnectWithAuthApi,
    setCallConnectWithAuthApi,

    // User data
    userData,
    setUserData,

    // Selected game management
    selectedGame,
    setSelectedGame,

    // User stats for current game
    currentUserStats,
    setCurrentUserStats,
    gameStats,
    setGameStats,

    // Last played games
    lastPlayedGames,
    setLastPlayedGames,

    // Modal state
    showAccountConnectModal,
    setShowAccountConnectModal,

    // Helper values
    isGameSelected,
    hasAuthToken,
  };

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>;
};
