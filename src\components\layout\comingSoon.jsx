const ComingSoon = ({ 
  title = "Coming Soon", 
  description = "This feature is currently under development and will be available soon.",
  icon = "🚀"
}) => {
  return (
    <div className="min-h-screen bg-slate-950 flex items-center justify-center p-6">
      <div className="text-center max-w-md mx-auto">
        {/* Icon */}
        <div className="w-24 h-24 bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-slate-700">
          <span className="text-4xl">{icon}</span>
        </div>

        {/* Title */}
        <h1 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent mb-4">
          {title}
        </h1>

        {/* Description */}
        <p className="text-gray-400 text-lg leading-relaxed mb-8">
          {description}
        </p>

        {/* Features Preview */}
        <div className="space-y-3 mb-8">
          <div className="flex items-center justify-center space-x-3 text-gray-500">
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
            <span className="text-sm">Epic features incoming</span>
          </div>
          <div className="flex items-center justify-center space-x-3 text-gray-500">
            <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
            <span className="text-sm">Enhanced gaming experience</span>
          </div>
          <div className="flex items-center justify-center space-x-3 text-gray-500">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
            <span className="text-sm">Revolutionary gameplay</span>
          </div>
        </div>

        {/* Back Button */}
        <button
          onClick={() => window.history.back()}
          className="bg-gradient-to-r from-slate-800 to-slate-700 hover:from-slate-700 hover:to-slate-600 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 border border-slate-600"
        >
          Go Back
        </button>
      </div>
    </div>
  );
};

export default ComingSoon;